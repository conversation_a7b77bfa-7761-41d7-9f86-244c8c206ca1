import { MMKV } from 'react-native-mmkv'
import { supabase } from './supabase'
import { localStorageService } from './localStorageService'
import { LoggedFood } from '../types/food'

const storage = new MMKV()
const MIGRATION_KEY = 'data_migration_completed'

export const migrationService = {
  async migrateUserDataIfNeeded(userId: string): Promise<void> {
    const migrationKey = `${MIGRATION_KEY}_${userId}`
    
    // Check if migration already completed for this user
    if (storage.getBoolean(migrationKey)) {
      console.log('Data migration already completed for user:', userId)
      return
    }

    console.log('Starting data migration from Supabase to local storage...')

    try {
      // Fetch all user's food logs from Supabase
      const { data, error } = await supabase
        .from('food_logs')
        .select(`
          id,
          food_name,
          serving_size,
          portion_estimate,
          quantity,
          meal_type,
          logged_at,
          meal_session_id,
          nutrition_data (
            calories,
            protein,
            carbs,
            fat,
            fiber,
            sugar,
            sodium,
            cholesterol,
            confidence
          )
        `)
        .eq('user_id', userId)
        .order('logged_at', { ascending: false })

      if (error) {
        console.error('Migration error fetching from Supabase:', error)
        return
      }

      if (!data || data.length === 0) {
        console.log('No existing data to migrate')
        storage.set(migrationKey, true)
        return
      }

      console.log(`Migrating ${data.length} food logs to local storage...`)

      // Transform and save to local storage
      const loggedFoods: LoggedFood[] = data.map((log: any) => ({
        id: log.id,
        name: log.food_name,
        serving_size: log.serving_size,
        portion_estimate: log.portion_estimate,
        quantity: log.quantity,
        meal_type: log.meal_type,
        logged_at: log.logged_at,
        meal_session_id: log.meal_session_id,
        calories: log.nutrition_data?.[0]?.calories || 0,
        protein: log.nutrition_data?.[0]?.protein || 0,
        carbs: log.nutrition_data?.[0]?.carbs || 0,
        fat: log.nutrition_data?.[0]?.fat || 0,
        fiber: log.nutrition_data?.[0]?.fiber || 0,
        sugar: log.nutrition_data?.[0]?.sugar || 0,
        sodium: log.nutrition_data?.[0]?.sodium || 0,
        cholesterol: log.nutrition_data?.[0]?.cholesterol || 0,
        confidence: log.nutrition_data?.[0]?.confidence || 0,
      }))

      // Save all foods to local storage
      localStorageService.saveFoodLogs(userId, loggedFoods)

      // Mark migration as completed
      storage.set(migrationKey, true)
      console.log(`✅ Successfully migrated ${loggedFoods.length} food logs to local storage!`)

    } catch (error) {
      console.error('Migration failed:', error)
    }
  },

  // Force re-migration (for testing)
  async forceMigration(userId: string): Promise<void> {
    const migrationKey = `${MIGRATION_KEY}_${userId}`
    storage.delete(migrationKey)
    await this.migrateUserDataIfNeeded(userId)
  },

  // Check if migration completed
  isMigrationCompleted(userId: string): boolean {
    const migrationKey = `${MIGRATION_KEY}_${userId}`
    return storage.getBoolean(migrationKey) || false
  }
}