interface FoodItem {
  id: string;
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber?: number;
  sugar?: number;
  sodium?: number;
  cholesterol?: number;
  serving_size: string;
  confidence: number;
  portion_estimate?: string;
}

interface RecognitionResult {
  overall_confidence: number;
  foods: FoodItem[];
  total_nutrition?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber?: number;
    sugar?: number;
    sodium?: number;
  };
}

class AIService {
  private apiKey: string | null = null;

  setApiKey(key: string): void {
    this.apiKey = key;
  }

  async recognizeFood(imageUri: string): Promise<RecognitionResult> {
    if (!this.apiKey) {
      throw new Error('OpenAI API key not configured. Please check your .env file.');
    }

    console.log('Analyzing food image with OpenAI...');
    return await this.recognizeWithOpenAI(imageUri);
  }

  private async recognizeWithOpenAI(imageUri: string): Promise<RecognitionResult> {
    if (!this.apiKey) {
      throw new Error('OpenAI API key not set');
    }
    
    console.log('Using OpenAI API key:', this.apiKey.substring(0, 20) + '...');
    const base64Image = await this.convertImageToBase64(imageUri);
    
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        response_format: { type: "json_object" },
        messages: [
          {
            role: 'system',
            content: 'You are a professional nutritionist and food recognition expert. Analyze food images with high accuracy, provide realistic portion estimates, and deliver comprehensive nutritional data. Always respond with valid JSON format. When uncertain about portion sizes, use common reference objects (coins, hands, plates) visible in the image for scale estimation. IMPORTANT: If no food is detected in the image, return {"foods": []} with an empty array. For low-quality, blurry, or poorly lit images, acknowledge the limitation and provide estimates with appropriately lowered confidence scores (0.3-0.5 range).'
          },
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: `Analyze this food image and identify ALL food items present. For each food item, provide detailed analysis:

REQUIREMENTS:
- Identify each distinct food item separately
- Estimate portion sizes using visible reference objects (plates, utensils, hands, coins if present)
- Provide confidence scores based on image clarity and food visibility
- Include comprehensive nutrition data per estimated serving
- If multiple servings of same food, list separately
- For unclear items, provide best estimate with lower confidence

PORTION SIZE GUIDELINES:
- Look for plate edges to estimate plate diameter (standard dinner plate = 10-12 inches)
- Use utensils as size references (fork = ~7 inches, spoon = ~6 inches)
- Consider food depth/height when visible
- For beverages: estimate container size (8oz cup, 12oz can, etc.)
- Use standard measurements: cups, ounces, grams, pieces
- Reference common objects: "size of a tennis ball", "palm-sized", "deck of cards"
- Consider typical serving sizes for each food type
- Default to conservative estimates when uncertain

Return valid JSON with this structure:
{
  "foods": [
    {
      "name": "Specific Food Name",
      "portion_estimate": "detailed portion description",
      "confidence": 0.85,
      "calories": 100,
      "protein": 5.0,
      "carbs": 20.0,
      "fat": 2.0,
      "fiber": 3.0,
      "sugar": 10.0,
      "sodium": 50,
      "cholesterol": 0
    }
  ]
}

Focus on accuracy over speed. Use USDA nutrition database standards for calculations.`
              },
              {
                type: 'image_url',
                image_url: {
                  url: `data:image/jpeg;base64,${base64Image}`,
                  detail: 'low'
                }
              }
            ]
          }
        ],
        max_tokens: 1500
      })
    });

    const result = await response.json();
    
    if (!response.ok) {
      const errorMessage = result.error?.message || 'Unknown error';
      if (response.status === 429) {
        throw new Error(`Rate limit exceeded. Please add credits to your OpenAI account or try again later.`);
      } else if (response.status === 401) {
        throw new Error(`Invalid API key. Please check your OpenAI API key configuration.`);
      } else if (response.status === 400) {
        throw new Error(`Invalid request. The image may be too large or corrupted.`);
      } else {
        throw new Error(`OpenAI API Error (${response.status}): ${errorMessage}`);
      }
    }
    
    return this.processOpenAIResult(result);
  }


  private async convertImageToBase64(imageUri: string): Promise<string> {
    console.log('Original imageUri:', imageUri);
    
    if (!imageUri || imageUri.trim() === '') {
      throw new Error('Image URI is empty or invalid');
    }

    // Clean up the URI - remove double file:// prefix if present
    const cleanUri = imageUri.replace(/^file:\/\/file:\/\//, 'file://');
    console.log('Cleaned imageUri:', cleanUri);

    const { Image } = await import('react-native-compressor');
    
    try {
      // Compress image before converting to base64
      const compressedUri = await Image.compress(cleanUri, {
        maxWidth: 1024,
        quality: 0.8,
        input: 'uri',
        output: 'jpg',
      });
      
      console.log('Compressed imageUri:', compressedUri);
      
      if (!compressedUri || compressedUri.trim() === '') {
        throw new Error('Compression returned empty URI');
      }
      
      const response = await fetch(compressedUri);
      const blob = await response.blob();
      
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const base64 = (reader.result as string).split(',')[1];
          resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (compressionError) {
      console.warn('Compression failed, using original URI:', compressionError);
      // Fallback to original image if compression fails
      const response = await fetch(cleanUri);
      const blob = await response.blob();
      
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const base64 = (reader.result as string).split(',')[1];
          resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    }
  }

  private processOpenAIResult(result: any): RecognitionResult {
    try {
      const content = result.choices?.[0]?.message?.content;
      if (!content) {
        throw new Error('No content in OpenAI response');
      }
      const parsed = JSON.parse(content);
      if (!parsed.foods || !Array.isArray(parsed.foods)) {
        throw new Error('Invalid response format. No foods detected in the image.');
      }
      const foods: FoodItem[] = parsed.foods.map((food: any, index: number) => ({
        id: `openai-${Date.now()}-${index}`,
        ...food
      }));
      const totalNutrition = this.calculateTotalNutrition(foods);
      const averageConfidence = foods.reduce((sum, food) => sum + food.confidence, 0) / foods.length;

      return {
        overall_confidence: averageConfidence,
        foods,
        total_nutrition: totalNutrition
      };
    } catch (error) {
      console.error('Failed to process OpenAI result:', error);
      throw new Error('Failed to analyze food image. Please try again with a clearer image.');
    }
  }


  private calculateTotalNutrition(foods: FoodItem[]) {
    return foods.reduce((total, food) => ({
      calories: total.calories + (food.calories || 0),
      protein: total.protein + (food.protein || 0),
      carbs: total.carbs + (food.carbs || 0),
      fat: total.fat + (food.fat || 0),
      fiber: (total.fiber || 0) + (food.fiber || 0),
      sugar: (total.sugar || 0) + (food.sugar || 0),
      sodium: (total.sodium || 0) + (food.sodium || 0),
      cholesterol: (total.cholesterol || 0) + (food.cholesterol || 0)
    }), {
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0,
      fiber: 0,
      sugar: 0,
      sodium: 0,
      cholesterol: 0
    });
  }
}

export const aiService = new AIService();
export type { FoodItem, RecognitionResult };