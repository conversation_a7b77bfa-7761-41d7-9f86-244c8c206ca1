import { supabase } from './supabase'
import { createClient } from '@supabase/supabase-js'
import { appleAuth } from '@invertase/react-native-apple-authentication'
import { Platform, Linking } from 'react-native'
import Config from 'react-native-config'

export const authService = {
  // Sign up with email and password
  async signUp(email: string, password: string) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    })
    return { data, error }
  },

  // Sign in with email and password
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    return { data, error }
  },

  // Sign out
  async signOut() {
    const { error } = await supabase.auth.signOut()
    return { error }
  },

  // Get current user
  async getCurrentUser() {
    const { data, error } = await supabase.auth.getUser()
    return { data, error }
  },

  // Listen to auth state changes
  onAuthStateChange(callback: (event: any, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback)
  },

  // Create user profile after signup
  async createProfile(userId: string, data: { username?: string; full_name?: string }) {
    const { data: profile, error } = await supabase
      .from('profiles')
      .insert([{ 
        id: userId, 
        ...data,
        // Set default goal values
        daily_calorie_goal: 2000,
        daily_protein_goal: 150,
        daily_carb_goal: 250,
        daily_fat_goal: 65
      }])
      .select()
      .single()
    
    return { data: profile, error }
  },

  // Get user profile
  async getProfile(userId: string) {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()
    
    // If profile exists but is missing goal values, initialize them
    if (data && (
      data.daily_calorie_goal === null || data.daily_calorie_goal === undefined ||
      data.daily_protein_goal === null || data.daily_protein_goal === undefined ||
      data.daily_carb_goal === null || data.daily_carb_goal === undefined ||
      data.daily_fat_goal === null || data.daily_fat_goal === undefined
    )) {
      console.log('Profile missing goal values, initializing defaults');
      const updateResult = await this.updateProfile(userId, {
        daily_calorie_goal: data.daily_calorie_goal || 2000,
        daily_protein_goal: data.daily_protein_goal || 150,
        daily_carb_goal: data.daily_carb_goal || 250,
        daily_fat_goal: data.daily_fat_goal || 65
      });
      
      if (!updateResult.error && updateResult.data) {
        return { data: updateResult.data, error: null };
      }
    }
    
    return { data, error }
  },

  // Update user profile
  async updateProfile(userId: string, updates: {
    username?: string;
    full_name?: string;
    daily_calorie_goal?: number;
    daily_protein_goal?: number;
    daily_carb_goal?: number;
    daily_fat_goal?: number;
  }) {
    // First check if profile exists
    const { data: existingProfile, error: checkError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', userId)
      .single()
    
    if (checkError || !existingProfile) {
      console.log('Profile does not exist for userId:', userId, 'creating new profile with defaults');
      // Try to create the profile with defaults if it doesn't exist
      const createResult = await this.createProfile(userId, {});
      if (createResult.error) {
        console.error('Failed to create profile:', createResult.error);
      }
      return createResult;
    }
    
    const { data, error } = await supabase
      .from('profiles')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId)
      .select()
      .single()
    
    if (error) {
      console.error('authService: updateProfile error:', error);
    }
    return { data, error }
  },

  // TODO: Sign in with Apple (iOS only) - Commented out until setup is complete
  /*
  async signInWithApple() {
    if (Platform.OS !== 'ios') {
      return { data: null, error: { message: 'Apple Sign-In is only available on iOS' } }
    }

    try {
      // Perform Apple authentication request
      const appleAuthRequestResponse = await appleAuth.performRequest({
        requestedOperation: appleAuth.Operation.LOGIN,
        requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME],
      })

      // Ensure Apple authentication was successful
      if (!appleAuthRequestResponse.identityToken) {
        throw new Error('Apple Sign-In failed - no identity token received')
      }

      // Sign in to Supabase with Apple credentials
      const { data, error } = await supabase.auth.signInWithIdToken({
        provider: 'apple',
        token: appleAuthRequestResponse.identityToken,
        nonce: appleAuthRequestResponse.nonce,
      })

      if (error) {
        return { data: null, error }
      }

      // Create profile if this is first time login and we have user data
      if (data.user && appleAuthRequestResponse.fullName) {
        const { givenName, familyName } = appleAuthRequestResponse.fullName
        const fullName = [givenName, familyName].filter(Boolean).join(' ')
        
        if (fullName) {
          await this.createProfile(data.user.id, { full_name: fullName })
        }
      }

      return { data, error: null }
    } catch (error: any) {
      return { data: null, error: { message: error.message || 'Apple Sign-In failed' } }
    }
  },
  */

  // TODO: Sign in with Google - Commented out until setup is complete
  /*
  async signInWithGoogle() {
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
      })

      if (error) {
        return { data: null, error }
      }

      // Profile will be created automatically by Supabase for OAuth users
      return { data, error: null }
    } catch (error: any) {
      return { data: null, error: { message: error.message || 'Google Sign-In failed' } }
    }
  },
  */

  // TODO: Check if Apple Sign-In is available (iOS 13+) - Commented out until setup is complete
  /*
  async isAppleSignInAvailable() {
    if (Platform.OS !== 'ios') return false
    try {
      return await appleAuth.isSupported
    } catch (error) {
      console.warn('Apple Sign-In availability check failed:', error)
      return false
    }
  },
  */

  // Sign in with Discord
  async signInWithDiscord() {
    try {
      console.log('Starting Discord OAuth...')
      
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'discord',
        options: {
          redirectTo: 'koa://auth',
        },
      })

      console.log('Discord OAuth response:', { data, error })

      if (error) {
        console.error('Discord OAuth error:', error)
        return { data: null, error }
      }

      if (data?.url) {
        console.log('Discord OAuth URL received:', data.url)
        // In React Native, we need to open the URL manually
        const { Linking } = require('react-native')
        await Linking.openURL(data.url)
      }

      // Profile will be created automatically by Supabase for OAuth users
      return { data, error: null }
    } catch (error: any) {
      console.error('Discord sign-in exception:', error)
      return { data: null, error: { message: error.message || 'Discord Sign-In failed' } }
    }
  },

  // Reset password - sends reset email
  async resetPasswordForEmail(email: string) {
    try {
      // Use hardcoded deep link URL since Linking.createURL doesn't exist in RN 0.80
      const redirectUrl = 'koa://auth'
      console.log('Password reset redirect URL:', redirectUrl)
      console.log('Sending password reset email to:', email)
      
      const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: redirectUrl,
      })
      
      console.log('Supabase resetPasswordForEmail response:', { data, error })
      return { data, error }
    } catch (err) {
      console.error('resetPasswordForEmail exception:', err)
      return { data: null, error: err }
    }
  },

  // Update password using recovery tokens directly
  async updatePasswordWithTokens(password: string, access_token: string, refresh_token: string) {
    try {
      console.log('Updating password with recovery tokens directly');
      
      // Create a temporary authenticated supabase client with the recovery tokens
      const tempClient = createClient(
        Config.SUPABASE_URL!,
        Config.SUPABASE_ANON_KEY!,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false, // Don't persist this temporary session
            detectSessionInUrl: false,
          }
        }
      );
      
      // Set the session with recovery tokens
      const { data: sessionData, error: sessionError } = await tempClient.auth.setSession({
        access_token,
        refresh_token,
      });
      
      if (sessionError || !sessionData.session) {
        console.error('Failed to set temporary session:', sessionError);
        return { data: null, error: sessionError || new Error('No session established') };
      }
      
      console.log('Temporary session established for password update');
      
      // Update password using the temporary client
      const { data, error } = await tempClient.auth.updateUser({
        password: password
      });
      
      console.log('Password update result:', { data, error });
      return { data, error };
    } catch (err) {
      console.error('updatePasswordWithTokens exception:', err);
      return { data: null, error: err };
    }
  },

  // Update user password (used after password recovery)
  async updatePassword(password: string) {
    try {
      console.log('Updating user password...');
      
      // Check current session before attempting update
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      console.log('Current session check:', { 
        hasSession: !!sessionData.session, 
        hasUser: !!sessionData.session?.user,
        sessionError: sessionError 
      });
      
      if (!sessionData.session) {
        console.error('No active session for password update');
        return { data: null, error: { message: 'No active session for password update' } };
      }
      
      const { data, error } = await supabase.auth.updateUser({
        password: password
      })
      console.log('Supabase updateUser response:', { data, error });
      return { data, error }
    } catch (err) {
      console.error('updatePassword exception:', err);
      return { data: null, error: err }
    }
  }
}