import { AppState } from 'react-native'
import { foodLogService } from './foodLogService'
import { syncQueueService } from './syncQueueService'
import { migrationService } from './migrationService'

class SyncService {
  private userId: string | null = null
  private isInitialized = false
  private appStateSubscription: any = null

  async initialize(userId: string) {
    if (this.isInitialized) return
    
    this.userId = userId
    this.isInitialized = true
    
    console.log('Initializing SyncService for user:', userId)

    // Enhanced AppState listener for sync queue processing
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange)
    
    // Initial sync check
    this.performSyncIfNeeded()

    // Run migration in background (non-blocking)
    migrationService.migrateUserDataIfNeeded(userId).then(() => {
      console.log('Background migration completed')
    }).catch(error => {
      console.error('Background migration failed:', error)
    })
  }

  private handleAppStateChange = async (nextAppState: string) => {
    console.log('App state changed to:', nextAppState)
    
    if (nextAppState === 'active' && this.userId) {
      console.log('App became active - checking for pending sync operations...')
      
      // Small delay to ensure app is fully active
      setTimeout(() => {
        this.performSyncIfNeeded()
      }, 1000)
    }
  }

  private async performSyncIfNeeded() {
    if (!this.userId) return

    const syncStatus = syncQueueService.getSyncStatus()
    
    if (syncStatus.isPending) {
      console.log(`Found ${syncStatus.pendingCount} pending sync operations, starting background sync...`)
      
      try {
        await foodLogService.forceSyncAll(this.userId)
        console.log('Background sync completed successfully')
      } catch (error) {
        console.log('Background sync failed:', error)
      }
    } else {
      console.log('No pending sync operations')
    }
  }

  // Manual sync trigger
  async triggerSync() {
    if (!this.userId) return
    
    console.log('Manual sync triggered')
    await this.performSyncIfNeeded()
  }

  // Get sync status
  getSyncStatus() {
    return syncQueueService.getSyncStatus()
  }

  // Clear sync queue (for testing)
  clearSyncQueue() {
    syncQueueService.clearAll()
  }

  // Get sync queue (for debugging)
  getSyncQueue() {
    return syncQueueService.getOperations()
  }

  // Migration helpers
  async forceMigration() {
    if (!this.userId) return
    await migrationService.forceMigration(this.userId)
  }

  isMigrationCompleted() {
    if (!this.userId) return false
    return migrationService.isMigrationCompleted(this.userId)
  }

  // Cleanup
  cleanup() {
    try {
      console.log('Cleaning up sync service...')
      if (this.isInitialized) {
        // Remove AppState listener using the stored subscription
        if (this.appStateSubscription) {
          this.appStateSubscription.remove()
          this.appStateSubscription = null
        }
        this.isInitialized = false
        this.userId = null
        console.log('Sync service cleanup completed')
      }
    } catch (error) {
      console.error('Error during sync service cleanup:', error)
      // Don't throw the error to avoid blocking sign out
      console.log('Continuing with cleanup despite error')
    }
  }
}

export const syncService = new SyncService()