import { MMKV } from 'react-native-mmkv'
import { LoggedFood, MealSession } from '../types/food'

const storage = new MMKV()

export const localStorageService = {
  // Food logs local storage
  getFoodLogs(userId: string, date?: string): LoggedFood[] {
    const key = date ? `food_logs_${userId}_${date}` : `food_logs_${userId}`
    const data = storage.getString(key)
    return data ? JSON.parse(data) : []
  },

  saveFoodLog(userId: string, food: LoggedFood): void {
    const today = new Date().toISOString().split('T')[0]
    const allFoods = this.getFoodLogs(userId)
    const todaysFoods = this.getFoodLogs(userId, today)
    
    // Add to today's foods
    const updatedTodaysFoods = [...todaysFoods, food]
    storage.set(`food_logs_${userId}_${today}`, JSON.stringify(updatedTodaysFoods))
    
    // Add to all foods
    const updatedAllFoods = [...allFoods, food]
    storage.set(`food_logs_${userId}`, JSON.stringify(updatedAllFoods))
  },

  saveFoodLogs(userId: string, foods: LoggedFood[]): void {
    foods.forEach(food => this.saveFoodLog(userId, food))
  },

  updateFoodLog(userId: string, foodId: string, updatedFood: Partial<LoggedFood>): void {
    const today = new Date().toISOString().split('T')[0]
    const todaysFoods = this.getFoodLogs(userId, today)
    const allFoods = this.getFoodLogs(userId)
    
    // Update in today's foods
    const updatedTodaysFoods = todaysFoods.map(food => 
      food.id === foodId ? { ...food, ...updatedFood } : food
    )
    storage.set(`food_logs_${userId}_${today}`, JSON.stringify(updatedTodaysFoods))
    
    // Update in all foods
    const updatedAllFoods = allFoods.map(food => 
      food.id === foodId ? { ...food, ...updatedFood } : food
    )
    storage.set(`food_logs_${userId}`, JSON.stringify(updatedAllFoods))
  },

  deleteFoodLog(userId: string, foodId: string): void {
    const today = new Date().toISOString().split('T')[0]
    const todaysFoods = this.getFoodLogs(userId, today)
    const allFoods = this.getFoodLogs(userId)
    
    // Remove from today's foods
    const updatedTodaysFoods = todaysFoods.filter(food => food.id !== foodId)
    storage.set(`food_logs_${userId}_${today}`, JSON.stringify(updatedTodaysFoods))
    
    // Remove from all foods
    const updatedAllFoods = allFoods.filter(food => food.id !== foodId)
    storage.set(`food_logs_${userId}`, JSON.stringify(updatedAllFoods))
  },

  deleteMealSession(userId: string, sessionId: string): void {
    const today = new Date().toISOString().split('T')[0]
    const todaysFoods = this.getFoodLogs(userId, today)
    const allFoods = this.getFoodLogs(userId)
    
    // Remove all foods with this meal_session_id from today
    const updatedTodaysFoods = todaysFoods.filter(food => food.meal_session_id !== sessionId)
    storage.set(`food_logs_${userId}_${today}`, JSON.stringify(updatedTodaysFoods))
    
    // Remove all foods with this meal_session_id from all foods
    const updatedAllFoods = allFoods.filter(food => food.meal_session_id !== sessionId)
    storage.set(`food_logs_${userId}`, JSON.stringify(updatedAllFoods))
  },

  // Get meal sessions (grouped by meal_session_id)
  getMealSessions(userId: string, date?: string): MealSession[] {
    const foods = this.getFoodLogs(userId, date)
    
    // Group by meal_session_id
    const sessionMap = new Map<string, LoggedFood[]>()
    
    for (const food of foods) {
      if (!food.meal_session_id) continue
      
      if (!sessionMap.has(food.meal_session_id)) {
        sessionMap.set(food.meal_session_id, [])
      }
      sessionMap.get(food.meal_session_id)!.push(food)
    }

    // Convert to MealSession objects
    const mealSessions: MealSession[] = Array.from(sessionMap.entries()).map(([sessionId, sessionFoods]) => {
      const totalCalories = sessionFoods.reduce((sum, food) => sum + (food.calories * food.quantity), 0)
      
      return {
        id: sessionId,
        meal_type: sessionFoods[0].meal_type,
        logged_at: sessionFoods[0].logged_at,
        foods: sessionFoods,
        total_calories: totalCalories,
        total_items: sessionFoods.length,
      }
    })

    // Sort by logged_at descending
    return mealSessions.sort((a, b) => new Date(b.logged_at).getTime() - new Date(a.logged_at).getTime())
  },

  // Clear all data (for testing/debugging)
  clearAllData(userId: string): void {
    const keys = storage.getAllKeys()
    keys.forEach(key => {
      if (key.includes(userId)) {
        storage.delete(key)
      }
    })
  }
}