import { FoodItem } from '../types/food';
import uuid from 'react-native-uuid';
import Config from 'react-native-config';

interface USDAFoodItem {
  fdcId: number;
  description: string;
  foodNutrients: Array<{
    nutrientId: number;
    value: number;
  }>;
  servingSize?: number;
  servingSizeUnit?: string;
}

interface USDASearchResponse {
  foods: USDAFoodItem[];
  totalHits: number;
}

class FoodSearchService {
  private readonly baseUrl = 'https://api.nal.usda.gov/fdc/v1';
  private readonly apiKey = Config.USDA_API_KEY || 'DEMO_KEY';
  private lastRequestTime = 0;
  private readonly minRequestInterval = 1100; // Slightly over 1 second to stay under rate limits

  constructor() {
    console.log(`FoodSearchService initialized with API key: ${this.apiKey.substring(0, 8)}...`);
  }
  
  // USDA nutrient IDs mapping
  private readonly nutrientMap = {
    energy: 1008, // Energy (kcal)
    protein: 1003, // Protein
    carbs: 1005, // Carbohydrate, by difference
    fat: 1004, // Total lipid (fat)
    fiber: 1079, // Fiber, total dietary
    sugar: 2000, // Total sugars
    sodium: 1093, // Sodium, Na
    cholesterol: 1253, // Cholesterol
  };

  async searchFoods(query: string, pageSize: number = 10): Promise<FoodItem[]> {
    if (!query || query.trim().length < 2) {
      return [];
    }

    try {
      // Rate limiting: ensure we don't make requests too frequently
      const now = Date.now();
      const timeSinceLastRequest = now - this.lastRequestTime;
      if (timeSinceLastRequest < this.minRequestInterval) {
        const waitTime = this.minRequestInterval - timeSinceLastRequest;
        console.log(`Rate limiting: waiting ${waitTime}ms before request`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
      this.lastRequestTime = Date.now();

      const params = new URLSearchParams({
        query: query.trim(),
        dataType: 'Foundation,SR Legacy', // High-quality data types
        pageSize: Math.min(pageSize, 20).toString(), // Limit to 20 to reduce API load
        api_key: this.apiKey,
      });
      
      console.log(`Making USDA API request for: "${query.trim()}"`);
      console.log(`API URL: ${this.baseUrl}/foods/search?${params.toString()}`);

      const response = await fetch(`${this.baseUrl}/foods/search?${params}`);
      
      console.log(`USDA API Response Status: ${response.status}`);
      console.log(`USDA API Response Headers:`, Object.fromEntries(response.headers.entries()));
      
      if (response.status === 429) {
        const responseText = await response.text();
        console.error('USDA API rate limit exceeded. This should not happen with 1000/hour limit.');
        console.error('Response body:', responseText);
        throw new Error(`USDA API rate limit exceeded: ${response.status}. Response: ${responseText}`);
      }
      
      if (!response.ok) {
        console.error(`USDA API error: ${response.status} - ${response.statusText}`);
        throw new Error(`USDA API error: ${response.status}`);
      }

      const data: USDASearchResponse = await response.json();
      
      return data.foods.map(this.mapUSDAToFoodItem);
    } catch (error) {
      console.error('Food search error:', error);
      throw error; // Re-throw to let the UI handle it
    }
  }

  private mapUSDAToFoodItem = (usdaFood: USDAFoodItem): FoodItem => {
    const getNutrientValue = (nutrientId: number): number => {
      const nutrient = usdaFood.foodNutrients.find(n => n.nutrientId === nutrientId);
      return nutrient?.value || 0;
    };

    // Clean and format the description
    const cleanName = this.cleanFoodName(usdaFood.description);
    
    // Determine serving size - default to 100g if not specified
    const servingSize = usdaFood.servingSize && usdaFood.servingSizeUnit 
      ? `${usdaFood.servingSize}${usdaFood.servingSizeUnit.toLowerCase()}`
      : '100g';

    return {
      id: uuid.v4() as string,
      name: cleanName,
      calories: Math.round(getNutrientValue(this.nutrientMap.energy)),
      protein: this.roundToOneDecimal(getNutrientValue(this.nutrientMap.protein)),
      carbs: this.roundToOneDecimal(getNutrientValue(this.nutrientMap.carbs)),
      fat: this.roundToOneDecimal(getNutrientValue(this.nutrientMap.fat)),
      serving_size: servingSize,
      confidence: 0.95, // USDA data is highly reliable
      fiber: this.roundToOneDecimal(getNutrientValue(this.nutrientMap.fiber)),
      sugar: this.roundToOneDecimal(getNutrientValue(this.nutrientMap.sugar)),
      sodium: Math.round(getNutrientValue(this.nutrientMap.sodium)),
      cholesterol: Math.round(getNutrientValue(this.nutrientMap.cholesterol)),
    };
  };

  private cleanFoodName(description: string): string {
    // Remove common USDA prefixes and suffixes, make title case
    return description
      .replace(/^(raw|cooked|prepared|fresh|frozen|canned|dried)\s+/i, '')
      .replace(/,\s*(raw|cooked|prepared|fresh|frozen|canned|dried)$/i, '')
      .toLowerCase()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  private roundToOneDecimal(value: number): number {
    return Math.round(value * 10) / 10;
  }

}

export const foodSearchService = new FoodSearchService();