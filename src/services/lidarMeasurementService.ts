import { NativeModules } from 'react-native';

const { LidarMeasurementModule } = NativeModules;

export interface MeasurementResult {
  distance: number; // in centimeters
  confidence: number; // 0-1
  quality: 'excellent' | 'good' | 'fair' | 'poor';
  measurements: {
    center: number;
    topLeft: number;
    topRight: number;
    bottomLeft: number;
    bottomRight: number;
  };
  processingTime: number; // in milliseconds
}

export interface CaptureWithDepthResult extends MeasurementResult {
  imagePath: string; // file:// path to saved JPEG
}


export interface MeasurementError {
  code: string;
  message: string;
}

class LidarMeasurementService {
  private isSessionActive: boolean = false;

  /**
   * Check if device supports LiDAR measurements
   */
  async isLidarSupported(): Promise<boolean> {
    try {
      return await LidarMeasurementModule.isLidarSupported();
    } catch (error) {
      console.error('❌ [LidarMeasurement] Error checking LiDAR support:', error);
      return false;
    }
  }

  /**
   * Perform a quick distance measurement using LiDAR
   * This will briefly start an ARKit session, take measurements, then stop
   */
  async measureDistance(): Promise<MeasurementResult> {
    console.log('📏 [LidarMeasurement] Starting distance measurement...');

    if (this.isSessionActive) {
      throw new Error('Measurement session already active');
    }

    try {
      this.isSessionActive = true;

      // Start brief ARKit session and get measurements
      const result = await LidarMeasurementModule.measureDistance();

      console.log('✅ [LidarMeasurement] Measurement complete:', {
        distance: result.distance,
        confidence: result.confidence,
        quality: result.quality
      });

      return result as MeasurementResult;
    } catch (error) {
      console.error('❌ [LidarMeasurement] Measurement failed:', error);
      throw error;
    } finally {
      this.isSessionActive = false;
      console.log('🏁 [LidarMeasurement] Session cleaned up');
    }
  }

  /**
   * Capture a photo via ARKit and return image path + depth metadata
   */
  async capturePhotoWithDepth(): Promise<CaptureWithDepthResult> {
    console.log('📸 [LidarMeasurement] Capturing photo with depth...');

    if (this.isSessionActive) {
      throw new Error('Measurement session already active');
    }

    try {
      this.isSessionActive = true;
      const result = await LidarMeasurementModule.capturePhotoWithDepth();
      if (!result?.imagePath) {
        throw new Error('No image path returned from native capture');
      }
      console.log('✅ [LidarMeasurement] Capture complete:', {
        imagePath: result.imagePath,
        distance: result.distance,
        quality: result.quality,
      });
      return result as CaptureWithDepthResult;
    } catch (error) {
      console.error('❌ [LidarMeasurement] Capture failed:', error);
      throw error;
    } finally {
      this.isSessionActive = false;
      console.log('🏁 [LidarMeasurement] Session cleaned up');
    }
  }

  /**
   * Check if a measurement session is currently active
   */
  isMeasuring(): boolean {
    return this.isSessionActive;
  }
}

export const lidarMeasurementService = new LidarMeasurementService();