import React from 'react';
import { TouchableOpacity, StyleSheet, View } from 'react-native';
import { BrutalistText } from './BrutalistText';
import { BrutalistTheme } from '../theme/colors';

interface AddFoodButtonProps {
  onPress: () => void;
  disabled?: boolean;
}

export const AddFoodButton: React.FC<AddFoodButtonProps> = ({ 
  onPress, 
  disabled = false 
}) => {
  return (
    <TouchableOpacity
      style={[styles.button, disabled && styles.disabled]}
      onPress={onPress}
      activeOpacity={disabled ? 1 : 0.7}
      disabled={disabled}
    >
      <BrutalistText
        variant="body"
        weight="medium"
        color={disabled ? 'grey500' : 'grey700'}
        fontFamily="mono"
        style={styles.buttonText}
      >
        + ADD ITEM
      </BrutalistText>
      <View style={[styles.underline, disabled && styles.disabledUnderline]} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    alignItems: 'center',
    paddingVertical: BrutalistTheme.spacing.sm,
    paddingHorizontal: BrutalistTheme.spacing.md,
  },
  disabled: {
    opacity: 0.5,
  },
  buttonText: {
    letterSpacing: 1,
  },
  underline: {
    height: 2,
    backgroundColor: BrutalistTheme.colors.grey700,
    marginTop: 2,
    width: '100%',
  },
  disabledUnderline: {
    backgroundColor: BrutalistTheme.colors.grey400,
  },
});