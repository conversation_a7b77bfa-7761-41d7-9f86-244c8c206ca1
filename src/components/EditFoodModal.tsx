import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { BrutalistText } from './BrutalistText';
import { BrutalistButton } from './BrutalistButton';
import { BrutalistTheme } from '../theme/colors';
import { LoggedFood } from '../types/food';

interface EditFoodModalProps {
  visible: boolean;
  food: LoggedFood | null;
  onSave: (editedFood: LoggedFood) => void;
  onCancel: () => void;
}

export const EditFoodModal: React.FC<EditFoodModalProps> = ({
  visible,
  food,
  onSave,
  onCancel,
}) => {
  const [editedFood, setEditedFood] = useState<LoggedFood | null>(null);

  useEffect(() => {
    if (food && visible) {
      setEditedFood({ ...food });
    }
  }, [food, visible]);

  const handleSave = () => {
    if (editedFood) {
      onSave(editedFood);
    }
  };

  const updateField = (field: keyof LoggedFood, value: string | number) => {
    if (editedFood) {
      setEditedFood(prev => prev ? { ...prev, [field]: value } : null);
    }
  };

  const parseNumber = (value: string): number => {
    const parsed = parseFloat(value);
    return isNaN(parsed) || parsed < 0 ? 0 : parsed;
  };

  if (!editedFood) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onCancel}
    >
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.container}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onCancel}
              activeOpacity={0.7}
            >
              <BrutalistText
                variant="callout"
                weight="bold"
                fontFamily="mono"
                color="grey700"
              >
                ✕
              </BrutalistText>
            </TouchableOpacity>
            
            <BrutalistText
              variant="title3"
              weight="bold"
              fontFamily="mono"
              color="grey800"
              style={styles.headerTitle}
            >
              EDIT FOOD ITEM
            </BrutalistText>
            
            <View style={styles.headerSpacer} />
          </View>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            <View style={styles.form}>
              {/* Food Name */}
              <View style={styles.inputGroup}>
                <BrutalistText
                  variant="caption1"
                  weight="medium"
                  fontFamily="mono"
                  color="grey700"
                  style={styles.label}
                >
                  FOOD NAME
                </BrutalistText>
                <TextInput
                  style={styles.textInput}
                  value={editedFood.name}
                  onChangeText={(text) => updateField('name', text)}
                  placeholder="Enter food name"
                  placeholderTextColor={BrutalistTheme.colors.grey500}
                />
              </View>

              {/* Quantity */}
              <View style={styles.inputGroup}>
                <BrutalistText
                  variant="caption1"
                  weight="medium"
                  fontFamily="mono"
                  color="grey700"
                  style={styles.label}
                >
                  QUANTITY
                </BrutalistText>
                <TextInput
                  style={styles.numberInput}
                  value={editedFood.quantity.toString()}
                  onChangeText={(text) => updateField('quantity', parseNumber(text))}
                  placeholder="1"
                  keyboardType="numeric"
                  placeholderTextColor={BrutalistTheme.colors.grey500}
                />
              </View>

              {/* Nutrition Values */}
              <View style={styles.nutritionSection}>
                <BrutalistText
                  variant="caption1"
                  weight="medium"
                  fontFamily="mono"
                  color="grey700"
                  style={styles.sectionLabel}
                >
                  NUTRITION (PER ITEM)
                </BrutalistText>

                <View style={styles.nutritionGrid}>
                  <View style={styles.nutritionItem}>
                    <BrutalistText
                      variant="caption2"
                      weight="medium"
                      color="grey600"
                      style={styles.nutritionLabel}
                    >
                      CALORIES
                    </BrutalistText>
                    <TextInput
                      style={styles.nutritionInput}
                      value={editedFood.calories.toString()}
                      onChangeText={(text) => updateField('calories', parseNumber(text))}
                      placeholder="0"
                      keyboardType="numeric"
                      placeholderTextColor={BrutalistTheme.colors.grey500}
                    />
                  </View>

                  <View style={styles.nutritionItem}>
                    <BrutalistText
                      variant="caption2"
                      weight="medium"
                      color="grey600"
                      style={styles.nutritionLabel}
                    >
                      PROTEIN (g)
                    </BrutalistText>
                    <TextInput
                      style={styles.nutritionInput}
                      value={editedFood.protein.toString()}
                      onChangeText={(text) => updateField('protein', parseNumber(text))}
                      placeholder="0"
                      keyboardType="numeric"
                      placeholderTextColor={BrutalistTheme.colors.grey500}
                    />
                  </View>

                  <View style={styles.nutritionItem}>
                    <BrutalistText
                      variant="caption2"
                      weight="medium"
                      color="grey600"
                      style={styles.nutritionLabel}
                    >
                      CARBS (g)
                    </BrutalistText>
                    <TextInput
                      style={styles.nutritionInput}
                      value={editedFood.carbs.toString()}
                      onChangeText={(text) => updateField('carbs', parseNumber(text))}
                      placeholder="0"
                      keyboardType="numeric"
                      placeholderTextColor={BrutalistTheme.colors.grey500}
                    />
                  </View>

                  <View style={styles.nutritionItem}>
                    <BrutalistText
                      variant="caption2"
                      weight="medium"
                      color="grey600"
                      style={styles.nutritionLabel}
                    >
                      FAT (g)
                    </BrutalistText>
                    <TextInput
                      style={styles.nutritionInput}
                      value={editedFood.fat.toString()}
                      onChangeText={(text) => updateField('fat', parseNumber(text))}
                      placeholder="0"
                      keyboardType="numeric"
                      placeholderTextColor={BrutalistTheme.colors.grey500}
                    />
                  </View>
                </View>
              </View>

              {/* Total Preview */}
              <View style={styles.previewSection}>
                <BrutalistText
                  variant="caption1"
                  weight="medium"
                  fontFamily="mono"
                  color="grey700"
                  style={styles.sectionLabel}
                >
                  TOTAL VALUES
                </BrutalistText>
                
                <View style={styles.previewGrid}>
                  <View style={styles.previewItem}>
                    <BrutalistText
                      variant="callout"
                      weight="semibold"
                      fontFamily="mono"
                      color="accent1"
                    >
                      {Math.round(editedFood.calories * editedFood.quantity)}
                    </BrutalistText>
                    <BrutalistText
                      variant="caption2"
                      weight="medium"
                      color="grey600"
                    >
                      CALORIES
                    </BrutalistText>
                  </View>

                  <View style={styles.previewItem}>
                    <BrutalistText
                      variant="callout"
                      weight="semibold"
                      fontFamily="mono"
                      color="grey800"
                    >
                      {(editedFood.protein * editedFood.quantity).toFixed(1)}g
                    </BrutalistText>
                    <BrutalistText
                      variant="caption2"
                      weight="medium"
                      color="grey600"
                    >
                      PROTEIN
                    </BrutalistText>
                  </View>

                  <View style={styles.previewItem}>
                    <BrutalistText
                      variant="callout"
                      weight="semibold"
                      fontFamily="mono"
                      color="grey800"
                    >
                      {(editedFood.carbs * editedFood.quantity).toFixed(1)}g
                    </BrutalistText>
                    <BrutalistText
                      variant="caption2"
                      weight="medium"
                      color="grey600"
                    >
                      CARBS
                    </BrutalistText>
                  </View>

                  <View style={styles.previewItem}>
                    <BrutalistText
                      variant="callout"
                      weight="semibold"
                      fontFamily="mono"
                      color="grey800"
                    >
                      {(editedFood.fat * editedFood.quantity).toFixed(1)}g
                    </BrutalistText>
                    <BrutalistText
                      variant="caption2"
                      weight="medium"
                      color="grey600"
                    >
                      FAT
                    </BrutalistText>
                  </View>
                </View>
              </View>
            </View>
          </ScrollView>

          {/* Action Buttons */}
          <View style={styles.actions}>
            <BrutalistButton
              title="CANCEL"
              variant="secondary"
              onPress={onCancel}
              style={styles.cancelButton}
            />
            <BrutalistButton
              title="SAVE CHANGES"
              variant="primary"
              onPress={handleSave}
              style={styles.saveButton}
            />
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BrutalistTheme.colors.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingTop: BrutalistTheme.spacing.lg,
    paddingBottom: BrutalistTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey200,
  },
  closeButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    letterSpacing: 1,
  },
  headerSpacer: {
    width: 44,
  },
  content: {
    flex: 1,
  },
  form: {
    padding: BrutalistTheme.spacing.lg,
    gap: BrutalistTheme.spacing.lg,
  },
  inputGroup: {
    gap: BrutalistTheme.spacing.xs,
  },
  label: {
    letterSpacing: 1,
    marginBottom: BrutalistTheme.spacing.xs,
  },
  textInput: {
    borderWidth: 2,
    borderColor: BrutalistTheme.colors.grey300,
    borderRadius: BrutalistTheme.borderRadius.sm,
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingVertical: BrutalistTheme.spacing.sm,
    fontSize: 16,
    fontFamily: BrutalistTheme.fonts.mono,
    color: BrutalistTheme.colors.grey800,
    backgroundColor: BrutalistTheme.colors.white,
  },
  numberInput: {
    borderWidth: 2,
    borderColor: BrutalistTheme.colors.grey300,
    borderRadius: BrutalistTheme.borderRadius.sm,
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingVertical: BrutalistTheme.spacing.sm,
    fontSize: 16,
    fontFamily: BrutalistTheme.fonts.mono,
    color: BrutalistTheme.colors.grey800,
    backgroundColor: BrutalistTheme.colors.white,
    textAlign: 'center',
    maxWidth: 100,
  },
  nutritionSection: {
    gap: BrutalistTheme.spacing.md,
  },
  sectionLabel: {
    letterSpacing: 1,
    textAlign: 'center',
  },
  nutritionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: BrutalistTheme.spacing.md,
    justifyContent: 'space-between',
  },
  nutritionItem: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    gap: BrutalistTheme.spacing.xs,
  },
  nutritionLabel: {
    textAlign: 'center',
  },
  nutritionInput: {
    borderWidth: 2,
    borderColor: BrutalistTheme.colors.grey300,
    borderRadius: BrutalistTheme.borderRadius.sm,
    paddingHorizontal: BrutalistTheme.spacing.sm,
    paddingVertical: BrutalistTheme.spacing.xs,
    fontSize: 14,
    fontFamily: BrutalistTheme.fonts.mono,
    color: BrutalistTheme.colors.grey800,
    backgroundColor: BrutalistTheme.colors.white,
    textAlign: 'center',
    width: 80,
  },
  previewSection: {
    backgroundColor: BrutalistTheme.colors.surface,
    borderRadius: BrutalistTheme.borderRadius.md,
    padding: BrutalistTheme.spacing.md,
    gap: BrutalistTheme.spacing.md,
  },
  previewGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  previewItem: {
    alignItems: 'center',
    flex: 1,
    gap: 2,
  },
  actions: {
    flexDirection: 'row',
    gap: BrutalistTheme.spacing.md,
    padding: BrutalistTheme.spacing.lg,
    borderTopWidth: 1,
    borderTopColor: BrutalistTheme.colors.grey200,
  },
  cancelButton: {
    flex: 1,
  },
  saveButton: {
    flex: 2,
  },
});