import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  TextInput,
  Alert,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { BrutalistText, BrutalistButton } from './index';
import { BrutalistTheme } from '../theme/colors';
import { useAuth } from '../contexts/AuthContext';
import { AppleSignInButton } from './AppleSignInButton';
import { GoogleSignInButton } from './GoogleSignInButton';

interface SignupScreenProps {
  onSwitchToLogin: () => void;
}

export const SignupScreen: React.FC<SignupScreenProps> = ({
  onSwitchToLogin,
}) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isAppleLoading, setIsAppleLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const {
    signUp,
    createProfile,
    signInWithApple,
    signInWithGoogle,
    isAppleSignInAvailable,
  } = useAuth();

  const validateForm = () => {
    if (!email.trim()) {
      Alert.alert('Error', 'Please enter your email address');
      return false;
    }
    if (!fullName.trim()) {
      Alert.alert('Error', 'Please enter your full name');
      return false;
    }
    if (password.length < 6) {
      Alert.alert('Error', 'Password must be at least 6 characters');
      return false;
    }
    if (password !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return false;
    }
    return true;
  };

  const handleSignup = async () => {
    if (!validateForm()) return;

    setIsLoading(true);

    // Sign up the user
    const { data, error } = await signUp(email.trim(), password);

    if (error) {
      Alert.alert('Signup Failed', error.message);
      setIsLoading(false);
      return;
    }

    // If signup successful
    if (data.user) {
      console.log('Signup successful - User:', data.user.email, 'Session:', !!data.session);
      
      // Only create profile if we have a session (email verification disabled)
      // If email verification is enabled, profile will be created after email confirmation
      if (data.session) {
        console.log('Creating profile immediately (email verification disabled)');
        const { error: profileError } = await createProfile({
          full_name: fullName.trim(),
        });

        if (profileError) {
          console.warn('Profile creation failed:', profileError.message);
        }
      } else {
        console.log('No session - email verification required, profile will be created after confirmation');
      }

      Alert.alert(
        'Account Created!',
        'Please check your email to verify your account, then return to the app.',
        [{ text: 'OK', onPress: onSwitchToLogin }],
      );
    }

    setIsLoading(false);
  };

  const handleAppleSignIn = async () => {
    setIsAppleLoading(true);
    const { error } = await signInWithApple();

    if (error) {
      Alert.alert('Apple Sign-In Failed', error.message);
    }

    setIsAppleLoading(false);
  };

  const handleGoogleSignIn = async () => {
    setIsGoogleLoading(true);
    const { error } = await signInWithGoogle();

    if (error) {
      Alert.alert('Google Sign-In Failed', error.message);
    }

    setIsGoogleLoading(false);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={BrutalistTheme.colors.white}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoid}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header - Clean like main app */}
          <View style={styles.header}>
            <BrutalistText
              variant="title1"
              fontFamily="system"
              weight="black"
              color="grey800"
            >
              KOA
            </BrutalistText>
            <BrutalistText
              variant="caption1"
              fontFamily="mono"
              weight="medium"
              color="grey600"
              style={styles.subtitle}
            >
              CREATE YOUR ACCOUNT
            </BrutalistText>
          </View>

          {/* Form - Clean inputs */}
          <View style={styles.form}>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={fullName}
                onChangeText={setFullName}
                placeholder="Full name"
                placeholderTextColor={BrutalistTheme.colors.grey500}
                autoCapitalize="words"
                returnKeyType="next"
              />
            </View>

            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={email}
                onChangeText={setEmail}
                placeholder="Email address"
                placeholderTextColor={BrutalistTheme.colors.grey500}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                returnKeyType="next"
              />
            </View>

            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={password}
                onChangeText={setPassword}
                placeholder="Password"
                placeholderTextColor={BrutalistTheme.colors.grey500}
                secureTextEntry
                returnKeyType="next"
              />
            </View>

            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                placeholder="Confirm password"
                placeholderTextColor={BrutalistTheme.colors.grey500}
                secureTextEntry
                returnKeyType="go"
                onSubmitEditing={handleSignup}
              />
            </View>

            <TouchableOpacity
              style={[
                styles.signupButton,
                (isLoading || isAppleLoading || isGoogleLoading) && styles.signupButtonDisabled
              ]}
              onPress={handleSignup}
              disabled={isLoading || isAppleLoading || isGoogleLoading}
              activeOpacity={0.7}
            >
              <BrutalistText
                variant="callout"
                weight="bold"
                color="white"
                fontFamily="system"
              >
                {isLoading ? 'Creating Account...' : 'Create Account'}
              </BrutalistText>
            </TouchableOpacity>

            {isAppleSignInAvailable && (
              <>
                <BrutalistText
                  variant="caption1"
                  color="grey500"
                  fontFamily="mono"
                  style={styles.dividerText}
                >
                  OR
                </BrutalistText>

                <View style={styles.socialButtons}>
                  <AppleSignInButton
                    onPress={handleAppleSignIn}
                    disabled={isLoading || isAppleLoading || isGoogleLoading}
                    loading={isAppleLoading}
                  />

                  <GoogleSignInButton
                    onPress={handleGoogleSignIn}
                    disabled={isLoading || isGoogleLoading}
                    loading={isGoogleLoading}
                  />
                </View>
              </>
            )}
          </View>

          {/* Footer - Subtle */}
          <View style={styles.footer}>
            <TouchableOpacity
              onPress={onSwitchToLogin}
              style={styles.switchButton}
              activeOpacity={0.7}
            >
              <BrutalistText
                variant="callout"
                weight="medium"
                color="grey600"
                fontFamily="mono"
              >
                Already have an account? Sign in
              </BrutalistText>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BrutalistTheme.colors.white,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingVertical: BrutalistTheme.spacing.xl,
  },

  // Header - Clean
  header: {
    alignItems: 'center',
    marginBottom: BrutalistTheme.spacing.xl,
    paddingTop: BrutalistTheme.spacing.xxl,
    gap: BrutalistTheme.spacing.xs,
  },
  subtitle: {
    letterSpacing: 1,
  },

  // Form - Clean
  form: {
    flex: 1,
    gap: BrutalistTheme.spacing.md,
  },
  inputContainer: {
    // No additional styling needed
  },
  input: {
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey300,
    paddingHorizontal: 0,
    paddingVertical: BrutalistTheme.spacing.md,
    fontSize: BrutalistTheme.fontSizes.body,
    fontFamily: BrutalistTheme.fonts.system,
    backgroundColor: BrutalistTheme.colors.white,
    color: BrutalistTheme.colors.grey800,
  },
  signupButton: {
    backgroundColor: BrutalistTheme.colors.black,
    paddingVertical: BrutalistTheme.spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: BrutalistTheme.spacing.lg,
    borderWidth: 1,
    borderColor: BrutalistTheme.colors.black,
    borderRadius: BrutalistTheme.borderRadius.sm,
  },
  signupButtonDisabled: {
    backgroundColor: BrutalistTheme.colors.grey400,
    borderColor: BrutalistTheme.colors.grey400,
  },

  // Social section - Simplified
  dividerText: {
    textAlign: 'center',
    letterSpacing: 1,
    marginVertical: BrutalistTheme.spacing.sm,
  },
  socialButtons: {
    flexDirection: 'row',
    gap: BrutalistTheme.spacing.sm,
  },

  // Footer - Subtle
  footer: {
    alignItems: 'center',
    paddingTop: BrutalistTheme.spacing.xl,
  },
  switchButton: {
    paddingVertical: BrutalistTheme.spacing.md,
    alignItems: 'center',
  },
});
