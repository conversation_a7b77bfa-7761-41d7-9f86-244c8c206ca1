import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { BrutalistTheme } from '../theme/colors';

interface BrutalistViewProps {
  children: React.ReactNode;
  variant?: 'card' | 'container' | 'section';
  backgroundColor?: keyof typeof BrutalistTheme.colors;
  borderColor?: keyof typeof BrutalistTheme.colors;
  padding?: keyof typeof BrutalistTheme.spacing;
  margin?: keyof typeof BrutalistTheme.spacing;
  style?: ViewStyle;
}

export const BrutalistView: React.FC<BrutalistViewProps> = ({
  children,
  variant = 'container',
  backgroundColor = 'white',
  borderColor = 'black',
  padding = 'md',
  margin,
  style,
}) => {
  const viewStyle = [
    styles.base,
    styles[variant],
    {
      backgroundColor: BrutalistTheme.colors[backgroundColor],
      borderColor: BrutalistTheme.colors[borderColor],
      padding: BrutalistTheme.spacing[padding],
      ...(margin && { margin: BrutalistTheme.spacing[margin] }),
    },
    style,
  ];

  return <View style={viewStyle}>{children}</View>;
};

const styles = StyleSheet.create({
  base: {
    borderWidth: BrutalistTheme.borderWidth.medium,
  },
  
  // Variants
  card: {
    borderWidth: BrutalistTheme.borderWidth.thick,
    borderRadius: BrutalistTheme.borderRadius.md,
    ...BrutalistTheme.shadows.strong,
  },
  container: {
    borderWidth: 0,
  },
  section: {
    borderWidth: BrutalistTheme.borderWidth.medium,
    borderRadius: BrutalistTheme.borderRadius.sm,
    marginVertical: BrutalistTheme.spacing.sm,
    ...BrutalistTheme.shadows.subtle,
  },
});