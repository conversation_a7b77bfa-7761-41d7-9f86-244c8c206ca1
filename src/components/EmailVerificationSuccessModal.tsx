import React from 'react';
import {
  View,
  StyleSheet,
  Modal,
  TouchableOpacity,
} from 'react-native';
import { BrutalistText } from './index';
import { BrutalistTheme } from '../theme/colors';

interface EmailVerificationSuccessModalProps {
  visible: boolean;
  onClose: () => void;
  userEmail?: string;
}

export const EmailVerificationSuccessModal: React.FC<EmailVerificationSuccessModalProps> = ({
  visible,
  onClose,
  userEmail,
}) => {
  // Auto-dismiss after 3 seconds to avoid blocking navigation
  React.useEffect(() => {
    if (visible) {
      const timer = setTimeout(() => {
        onClose();
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [visible, onClose]);

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modal}>
          {/* Success Icon */}
          <View style={styles.iconContainer}>
            <BrutalistText
              variant="largeTitle"
              weight="bold"
              color="accent1"
              style={styles.successIcon}
            >
              ✅
            </BrutalistText>
          </View>

          {/* Success Message */}
          <View style={styles.content}>
            <BrutalistText
              variant="title3"
              weight="bold"
              color="grey900"
              fontFamily="system"
              style={styles.title}
            >
              Email Verified!
            </BrutalistText>

            <BrutalistText
              variant="body"
              color="grey700"
              fontFamily="system"
              style={styles.message}
            >
              Welcome to KOA! Your account has been verified and you're now signed in.
            </BrutalistText>

            <BrutalistText
              variant="caption2"
              color="grey500"
              fontFamily="mono"
              style={styles.autoClose}
            >
              Automatically closing in 3 seconds...
            </BrutalistText>

            {userEmail && (
              <BrutalistText
                variant="caption1"
                color="grey600"
                fontFamily="mono"
                style={styles.email}
              >
                {userEmail}
              </BrutalistText>
            )}
          </View>

          {/* Continue Button */}
          <TouchableOpacity
            style={styles.continueButton}
            onPress={onClose}
            activeOpacity={0.7}
          >
            <BrutalistText
              variant="callout"
              weight="bold"
              color="white"
              fontFamily="system"
            >
              Continue to App
            </BrutalistText>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: BrutalistTheme.spacing.lg,
  },
  modal: {
    backgroundColor: BrutalistTheme.colors.white,
    borderRadius: BrutalistTheme.borderRadius.md,
    padding: BrutalistTheme.spacing.xl,
    alignItems: 'center',
    maxWidth: 320,
    width: '100%',
    borderWidth: 1,
    borderColor: BrutalistTheme.colors.grey200,
  },
  iconContainer: {
    marginBottom: BrutalistTheme.spacing.lg,
  },
  successIcon: {
    fontSize: 48,
  },
  content: {
    alignItems: 'center',
    marginBottom: BrutalistTheme.spacing.xl,
    gap: BrutalistTheme.spacing.sm,
  },
  title: {
    textAlign: 'center',
  },
  message: {
    textAlign: 'center',
    lineHeight: 22,
  },
  email: {
    textAlign: 'center',
    letterSpacing: 0.5,
    marginTop: BrutalistTheme.spacing.xs,
  },
  autoClose: {
    textAlign: 'center',
    letterSpacing: 0.5,
    fontStyle: 'italic',
  },
  continueButton: {
    backgroundColor: BrutalistTheme.colors.black,
    paddingVertical: BrutalistTheme.spacing.md,
    paddingHorizontal: BrutalistTheme.spacing.xl,
    borderRadius: BrutalistTheme.borderRadius.sm,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    borderWidth: 1,
    borderColor: BrutalistTheme.colors.black,
  },
});