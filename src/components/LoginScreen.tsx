import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  TextInput,
  Alert,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { BrutalistText, BrutalistButton } from './index';
import { BrutalistTheme } from '../theme/colors';
import { useAuth } from '../contexts/AuthContext';
// import { AppleSignInButton } from './AppleSignInButton';
// import { GoogleSignInButton } from './GoogleSignInButton';
import { DiscordSignInButton } from './DiscordSignInButton';

interface LoginScreenProps {
  onSwitchToSignup: () => void;
  onSwitchToSms?: () => void;
  onSwitchToForgotPassword: () => void;
}

export const LoginScreen: React.FC<LoginScreenProps> = ({
  onSwitchToSignup,
  onSwitchToForgotPassword,
}) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  // const [isAppleLoading, setIsAppleLoading] = useState(false);
  // const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [isDiscordLoading, setIsDiscordLoading] = useState(false);
  const { signIn, signInWithDiscord } = useAuth();
  // const { signIn, signInWithApple, signInWithGoogle, isAppleSignInAvailable } = useAuth();

  const handleLogin = async () => {
    if (!email.trim() || !password.trim()) {
      Alert.alert('Error', 'Please enter both email and password');
      return;
    }

    setIsLoading(true);
    const { error } = await signIn(email.trim(), password);

    if (error) {
      Alert.alert('Login Failed', error.message);
    }

    setIsLoading(false);
  };

  // const handleAppleSignIn = async () => {
  //   setIsAppleLoading(true);
  //   const { error } = await signInWithApple();

  //   if (error) {
  //     Alert.alert('Apple Sign-In Failed', error.message);
  //   }

  //   setIsAppleLoading(false);
  // };

  // const handleGoogleSignIn = async () => {
  //   setIsGoogleLoading(true);
  //   const { error } = await signInWithGoogle();

  //   if (error) {
  //     Alert.alert('Google Sign-In Failed', error.message);
  //   }

  //   setIsGoogleLoading(false);
  // };

  const handleDiscordSignIn = async () => {
    setIsDiscordLoading(true);
    const { error } = await signInWithDiscord();

    if (error) {
      Alert.alert('Discord Sign-In Failed', error.message);
    }

    setIsDiscordLoading(false);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={BrutalistTheme.colors.white}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoid}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header - Clean like main app */}
          <View style={styles.header}>
            <BrutalistText
              variant="title1"
              fontFamily="system"
              weight="black"
              color="grey800"
            >
              KOA
            </BrutalistText>
            <BrutalistText
              variant="caption1"
              fontFamily="mono"
              weight="medium"
              color="grey600"
              style={styles.subtitle}
            >
              SIGN IN TO YOUR ACCOUNT
            </BrutalistText>
          </View>

          {/* Form - Clean inputs */}
          <View style={styles.form}>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={email}
                onChangeText={setEmail}
                placeholder="Email address"
                placeholderTextColor={BrutalistTheme.colors.grey500}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                returnKeyType="next"
              />
            </View>

            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={password}
                onChangeText={setPassword}
                placeholder="Password"
                placeholderTextColor={BrutalistTheme.colors.grey500}
                secureTextEntry
                returnKeyType="go"
                onSubmitEditing={handleLogin}
              />
            </View>

            <TouchableOpacity
              style={[
                styles.loginButton,
                (isLoading || isDiscordLoading) && styles.loginButtonDisabled
              ]}
              onPress={handleLogin}
              disabled={isLoading || isDiscordLoading}
              activeOpacity={0.7}
            >
              <BrutalistText
                variant="callout"
                weight="bold"
                color="white"
                fontFamily="system"
              >
                {isLoading ? 'Signing In...' : 'Sign In'}
              </BrutalistText>
            </TouchableOpacity>

            <BrutalistText
              variant="caption1"
              color="grey500"
              fontFamily="mono"
              style={styles.dividerText}
            >
              OR
            </BrutalistText>

            <View style={styles.socialButtons}>
              <DiscordSignInButton
                onPress={handleDiscordSignIn}
                disabled={isLoading || isDiscordLoading}
                loading={isDiscordLoading}
              />
            </View>

{/* TODO: Uncomment when Apple/Google auth is properly configured
            {isAppleSignInAvailable && (
              <>
                <View style={styles.socialButtons}>
                  <AppleSignInButton
                    onPress={handleAppleSignIn}
                    disabled={isLoading || isAppleLoading || isGoogleLoading}
                    loading={isAppleLoading}
                  />

                  <GoogleSignInButton
                    onPress={handleGoogleSignIn}
                    disabled={isLoading || isGoogleLoading}
                    loading={isGoogleLoading}
                  />
                </View>
              </>
            )}
            */}
          </View>

          {/* Footer - Subtle */}
          <View style={styles.footer}>
            <TouchableOpacity
              onPress={onSwitchToForgotPassword}
              style={styles.forgotPasswordButton}
              activeOpacity={0.7}
            >
              <BrutalistText
                variant="callout"
                weight="medium"
                color="grey600"
                fontFamily="mono"
              >
                Forgot your password?
              </BrutalistText>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={onSwitchToSignup}
              style={styles.switchButton}
              activeOpacity={0.7}
            >
              <BrutalistText
                variant="callout"
                weight="medium"
                color="grey600"
                fontFamily="mono"
              >
                Don't have an account? Create one
              </BrutalistText>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BrutalistTheme.colors.white,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingVertical: BrutalistTheme.spacing.xl,
  },

  // Header - Clean
  header: {
    alignItems: 'center',
    marginBottom: BrutalistTheme.spacing.xl,
    paddingTop: BrutalistTheme.spacing.xxl,
    gap: BrutalistTheme.spacing.xs,
  },
  subtitle: {
    letterSpacing: 1,
  },

  // Form - Clean
  form: {
    flex: 1,
    gap: BrutalistTheme.spacing.md,
  },
  inputContainer: {
    // No additional styling needed
  },
  input: {
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey300,
    paddingHorizontal: 0,
    paddingVertical: BrutalistTheme.spacing.md,
    fontSize: BrutalistTheme.fontSizes.body,
    fontFamily: BrutalistTheme.fonts.system,
    backgroundColor: BrutalistTheme.colors.white,
    color: BrutalistTheme.colors.grey800,
  },
  loginButton: {
    backgroundColor: BrutalistTheme.colors.black,
    paddingVertical: BrutalistTheme.spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: BrutalistTheme.spacing.lg,
    borderWidth: 1,
    borderColor: BrutalistTheme.colors.black,
    borderRadius: BrutalistTheme.borderRadius.sm,
  },
  loginButtonDisabled: {
    backgroundColor: BrutalistTheme.colors.grey400,
    borderColor: BrutalistTheme.colors.grey400,
  },
  forgotPasswordButton: {
    alignItems: 'center',
    paddingVertical: BrutalistTheme.spacing.md,
  },

  // Social section - Simplified
  dividerText: {
    textAlign: 'center',
    letterSpacing: 1,
    marginVertical: BrutalistTheme.spacing.sm,
  },
  socialButtons: {
    flexDirection: 'row',
    gap: BrutalistTheme.spacing.sm,
  },

  // Footer - Subtle
  footer: {
    alignItems: 'center',
    paddingTop: BrutalistTheme.spacing.xl,
  },
  switchButton: {
    paddingVertical: BrutalistTheme.spacing.md,
    alignItems: 'center',
  },
});
