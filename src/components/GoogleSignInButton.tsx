import React from 'react'
import {
  TouchableOpacity,
  View,
  StyleSheet,
} from 'react-native'
import { BrutalistText } from './BrutalistText'
import { BrutalistTheme } from '../theme/colors'

interface GoogleSignInButtonProps {
  onPress: () => void
  disabled?: boolean
  loading?: boolean
}

export const GoogleSignInButton: React.FC<GoogleSignInButtonProps> = ({
  onPress,
  disabled = false,
  loading = false,
}) => {
  return (
    <TouchableOpacity
      style={[styles.button, disabled && styles.disabled]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.8}
    >
      <BrutalistText
        variant="callout"
        weight="bold"
        color="black"
        fontFamily="mono"
      >
        {loading ? 'SIGNING IN...' : 'GOOGLE'}
      </BrutalistText>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  button: {
    backgroundColor: BrutalistTheme.colors.white,
    borderWidth: BrutalistTheme.borderWidth.medium,
    borderColor: BrutalistTheme.colors.black,
    borderRadius: BrutalistTheme.borderRadius.sm,
    minHeight: 48,
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingVertical: BrutalistTheme.spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    ...BrutalistTheme.shadows.subtle,
  },
  disabled: {
    backgroundColor: BrutalistTheme.colors.grey200,
    borderColor: BrutalistTheme.colors.grey400,
  },
})