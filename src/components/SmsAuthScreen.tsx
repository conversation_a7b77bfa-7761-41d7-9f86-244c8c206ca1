import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  TextInput,
  Alert,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { BrutalistText, BrutalistButton } from './index';
import { BrutalistTheme } from '../theme/colors';
import { authService } from '../services/authService';

interface SmsAuthScreenProps {
  onBack: () => void;
}

export const SmsAuthScreen: React.FC<SmsAuthScreenProps> = ({ onBack }) => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otp, setOtp] = useState('');
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);

  const formatPhoneNumber = (text: string) => {
    // Simple US phone number formatting
    const cleaned = text.replace(/\D/g, '');
    if (cleaned.length <= 3) return cleaned;
    if (cleaned.length <= 6)
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(
      6,
      10,
    )}`;
  };

  const handleSendOtp = async () => {
    const cleaned = phoneNumber.replace(/\D/g, '');
    if (cleaned.length !== 10) {
      Alert.alert('Error', 'Please enter a valid 10-digit phone number');
      return;
    }

    const formattedPhone = `+1${cleaned}`; // US numbers only for now
    setIsLoading(true);

    const { error } = await authService.sendSmsOtp(formattedPhone);

    if (error) {
      Alert.alert('Error', error.message);
    } else {
      setIsOtpSent(true);
      Alert.alert(
        'Code Sent',
        'Please check your phone for the verification code',
      );
    }

    setIsLoading(false);
  };

  const handleVerifyOtp = async () => {
    if (otp.length !== 6) {
      Alert.alert('Error', 'Please enter the 6-digit verification code');
      return;
    }

    const cleaned = phoneNumber.replace(/\D/g, '');
    const formattedPhone = `+1${cleaned}`;
    setIsVerifying(true);

    const { error } = await authService.verifySmsOtp(formattedPhone, otp);

    if (error) {
      Alert.alert('Verification Failed', error.message);
    }

    setIsVerifying(false);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={BrutalistTheme.colors.white}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoid}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity onPress={onBack} style={styles.backButton}>
              <BrutalistText
                variant="body"
                weight="medium"
                color="black"
                fontFamily="mono"
              >
                ← BACK
              </BrutalistText>
            </TouchableOpacity>

            <BrutalistText
              variant="brutalistHero"
              fontFamily="mono"
              weight="black"
              color="black"
            >
              KOA
            </BrutalistText>
            <BrutalistText
              variant="title3"
              fontFamily="mono"
              weight="medium"
              color="accent1"
              style={styles.subtitle}
            >
              {isOtpSent ? 'VERIFY CODE' : 'PHONE SIGN IN'}
            </BrutalistText>
          </View>

          {/* Form */}
          <View style={styles.form}>
            {!isOtpSent ? (
              <View style={styles.inputContainer}>
                <BrutalistText
                  variant="caption1"
                  weight="medium"
                  color="grey700"
                  fontFamily="mono"
                  style={styles.inputLabel}
                >
                  PHONE NUMBER
                </BrutalistText>
                <TextInput
                  style={styles.input}
                  value={phoneNumber}
                  onChangeText={text => setPhoneNumber(formatPhoneNumber(text))}
                  placeholder="(*************"
                  placeholderTextColor={BrutalistTheme.colors.grey500}
                  keyboardType="phone-pad"
                  maxLength={14}
                  returnKeyType="go"
                  onSubmitEditing={handleSendOtp}
                />

                <BrutalistButton
                  title={isLoading ? 'SENDING CODE...' : 'SEND CODE'}
                  onPress={handleSendOtp}
                  disabled={isLoading}
                  variant="primary"
                  size="large"
                  style={styles.sendButton}
                />
              </View>
            ) : (
              <View style={styles.inputContainer}>
                <BrutalistText
                  variant="caption1"
                  weight="medium"
                  color="grey700"
                  fontFamily="mono"
                  style={styles.inputLabel}
                >
                  VERIFICATION CODE
                </BrutalistText>
                <BrutalistText
                  variant="caption2"
                  color="accent1"
                  style={styles.hint}
                >
                  Enter the 6-digit code sent to {phoneNumber}
                </BrutalistText>
                <TextInput
                  style={styles.input}
                  value={otp}
                  onChangeText={setOtp}
                  placeholder="123456"
                  placeholderTextColor={BrutalistTheme.colors.grey500}
                  keyboardType="number-pad"
                  maxLength={6}
                  returnKeyType="go"
                  onSubmitEditing={handleVerifyOtp}
                />

                <BrutalistButton
                  title={isVerifying ? 'VERIFYING...' : 'VERIFY CODE'}
                  onPress={handleVerifyOtp}
                  disabled={isVerifying}
                  variant="primary"
                  size="large"
                  style={styles.verifyButton}
                />

                <TouchableOpacity
                  onPress={() => setIsOtpSent(false)}
                  style={styles.changeNumberButton}
                >
                  <BrutalistText
                    variant="body"
                    weight="medium"
                    color="secondary"
                    fontFamily="mono"
                  >
                    CHANGE NUMBER
                  </BrutalistText>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BrutalistTheme.colors.white,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingVertical: BrutalistTheme.spacing.xl,
  },

  // Header
  header: {
    alignItems: 'center',
    marginBottom: BrutalistTheme.spacing.xxxl,
    paddingTop: BrutalistTheme.spacing.lg,
  },
  backButton: {
    position: 'absolute',
    top: 0,
    left: 0,
    paddingVertical: BrutalistTheme.spacing.xs,
  },
  subtitle: {
    marginTop: BrutalistTheme.spacing.sm,
    letterSpacing: 2,
  },

  // Form
  form: {
    flex: 1,
    gap: BrutalistTheme.spacing.lg,
  },
  inputContainer: {
    gap: BrutalistTheme.spacing.xs,
  },
  inputLabel: {
    letterSpacing: 1,
  },
  hint: {
    marginBottom: BrutalistTheme.spacing.sm,
    textAlign: 'center',
  },
  input: {
    borderWidth: BrutalistTheme.borderWidth.medium,
    borderColor: BrutalistTheme.colors.black,
    borderRadius: BrutalistTheme.borderRadius.sm,
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingVertical: BrutalistTheme.spacing.md,
    fontSize: BrutalistTheme.fontSizes.body,
    fontFamily: BrutalistTheme.fonts.system,
    backgroundColor: BrutalistTheme.colors.white,
    minHeight: 56,
    textAlign: 'center',
    ...BrutalistTheme.shadows.subtle,
  },
  sendButton: {
    marginTop: BrutalistTheme.spacing.md,
  },
  verifyButton: {
    marginTop: BrutalistTheme.spacing.md,
  },
  changeNumberButton: {
    marginTop: BrutalistTheme.spacing.lg,
    alignItems: 'center',
    paddingVertical: BrutalistTheme.spacing.sm,
  },
});
