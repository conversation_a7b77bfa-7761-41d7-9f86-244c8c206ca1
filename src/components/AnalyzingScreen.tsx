import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  StyleSheet,
  Animated,
  StatusBar,
} from 'react-native';
import { BrutalistText } from './BrutalistText';
import { BrutalistTheme } from '../theme/colors';

interface AnalyzingScreenProps {
  onComplete?: () => void;
}

export const AnalyzingScreen: React.FC<AnalyzingScreenProps> = ({ onComplete }) => {
  const circleAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    // Circle rotation animation
    const circleRotation = Animated.loop(
      Animated.timing(circleAnim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: true,
      }),
    );

    // Fade in animation
    const fadeIn = Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    });

    // Scale in animation
    const scaleIn = Animated.spring(scaleAnim, {
      toValue: 1,
      tension: 50,
      friction: 7,
      useNativeDriver: true,
    });

    // Start all animations
    circleRotation.start();
    fadeIn.start();
    scaleIn.start();

    return () => {
      circleRotation.stop();
      fadeIn.stop();
      scaleIn.stop();
    };
  }, []);

  const rotate = circleAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="black" />
      
      <Animated.View 
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }]
          }
        ]}
      >
        {/* Main Circle Loader */}
        <View style={styles.loaderContainer}>
          <Animated.View 
            style={[
              styles.outerCircle,
              { transform: [{ rotate }] }
            ]}
          >
            <View style={styles.circleSegment} />
          </Animated.View>
          <View style={styles.innerCircle} />
        </View>

        {/* Text Content */}
        <View style={styles.textContainer}>
          <BrutalistText 
            variant="title2"
            weight="bold"
            color="white"
            fontFamily="mono"
            style={styles.mainText}
          >
            SCANNING
          </BrutalistText>
          <BrutalistText 
            variant="body"
            weight="medium"
            color="grey400"
            fontFamily="mono"
            style={styles.subText}
          >
            Identifying food...
          </BrutalistText>
        </View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BrutalistTheme.colors.black,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    gap: BrutalistTheme.spacing.xxl,
  },
  loaderContainer: {
    width: 140,
    height: 140,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  outerCircle: {
    width: 140,
    height: 140,
    borderRadius: 70,
    borderWidth: BrutalistTheme.borderWidth.thick,
    borderColor: 'transparent',
    borderTopColor: BrutalistTheme.colors.primary,
    borderRightColor: BrutalistTheme.colors.primary,
    position: 'absolute',
  },
  circleSegment: {
    width: '100%',
    height: '100%',
    borderRadius: 70,
  },
  innerCircle: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: BrutalistTheme.colors.white,
    borderWidth: BrutalistTheme.borderWidth.thick,
    borderColor: BrutalistTheme.colors.black,
  },
  textContainer: {
    alignItems: 'center',
    gap: BrutalistTheme.spacing.sm,
  },
  mainText: {
    letterSpacing: 3,
    textAlign: 'center',
  },
  subText: {
    letterSpacing: 1,
    textAlign: 'center',
  },
});