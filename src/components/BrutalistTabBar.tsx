import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { BrutalistText } from './BrutalistText';
import { BrutalistTheme } from '../theme/colors';

export type TabKey = 'scan' | 'today' | 'history';

interface Tab {
  key: TabKey;
  label: string;
  icon?: string; // Simple text icons for now, can be replaced with proper icons later
}

interface BrutalistTabBarProps {
  activeTab: TabKey;
  onTabPress: (tab: TabKey) => void;
}

const tabs: Tab[] = [
  { key: 'scan', label: 'SCAN' },
  { key: 'today', label: 'TODAY' },
  { key: 'history', label: 'HISTORY' },
];

export const BrutalistTabBar: React.FC<BrutalistTabBarProps> = ({
  activeTab,
  onTabPress,
}) => {
  return (
    <View style={styles.container}>
      {tabs.map(tab => {
        const isActive = activeTab === tab.key;

        return (
          <TouchableOpacity
            key={tab.key}
            style={styles.tab}
            onPress={() => onTabPress(tab.key)}
            activeOpacity={0.7}
          >
            <BrutalistText
              variant="callout"
              weight="black"
              fontFamily="system"
              color={isActive ? 'black' : 'grey600'}
              style={[styles.tabLabel, isActive && styles.activeTabLabel]}
            >
              {tab.label}
            </BrutalistText>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: BrutalistTheme.colors.white,
    paddingTop: BrutalistTheme.spacing.md,
    paddingBottom: 0,
    paddingHorizontal: BrutalistTheme.spacing.lg,
    borderTopWidth: BrutalistTheme.borderWidth.hairline,
    borderTopColor: BrutalistTheme.colors.grey300,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 0,
  },
  tabLabel: {
    textAlign: 'center',
    letterSpacing: 1.1,
    fontSize: 15,
  },
  activeTabLabel: {
    fontSize: 16,
    letterSpacing: 1.3,
  },
});
