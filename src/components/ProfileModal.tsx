import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Modal,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  ScrollView,
  TextInput,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { BrutalistText } from './BrutalistText';
import { BrutalistButton } from './BrutalistButton';
import { BrutalistTheme } from '../theme/colors';
import { useAuth } from '../contexts/AuthContext';

interface ProfileModalProps {
  visible: boolean;
  onClose: () => void;
}

export const ProfileModal: React.FC<ProfileModalProps> = ({
  visible,
  onClose,
}) => {
  const { user, profile, signOut, updateProfile } = useAuth();
  
  // Local state for editing values (like MealEditModal)
  const [localGoals, setLocalGoals] = useState({
    calories: 2000,
    protein: 150,
    carbs: 250,
    fat: 65
  });

  // Update local state when profile changes or modal becomes visible
  useEffect(() => {
    if (visible && profile) {
      setLocalGoals({
        calories: profile.daily_calorie_goal ?? 2000,
        protein: profile.daily_protein_goal ?? 150,
        carbs: profile.daily_carb_goal ?? 250,
        fat: profile.daily_fat_goal ?? 65
      });
    } else if (visible && !profile) {
      // Set defaults for new users while profile is being created
      setLocalGoals({
        calories: 2000,
        protein: 150,
        carbs: 250,
        fat: 65
      });
    }
  }, [profile, visible]);

  const handleLogout = () => {
    Alert.alert(
      'SIGN OUT',
      'Are you sure you want to sign out of your account?',
      [
        {
          text: 'CANCEL',
          style: 'cancel',
        },
        {
          text: 'SIGN OUT',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await signOut();
              if (error) {
                Alert.alert('Error', 'Failed to sign out. Please try again.');
              } else {
                onClose();
              }
            } catch (err) {
              Alert.alert('Error', 'Failed to sign out. Please try again.');
            }
          },
        },
      ]
    );
  };

  // Get user initials for large avatar
  const getInitials = () => {
    if (profile?.full_name) {
      return profile.full_name
        .split(' ')
        .map(name => name.charAt(0).toUpperCase())
        .slice(0, 2)
        .join('');
    }
    if (profile?.username) {
      return profile.username.charAt(0).toUpperCase();
    }
    if (user?.email) {
      return user.email.charAt(0).toUpperCase();
    }
    return '?';
  };

  const formatGoal = (value: number, unit: string) => {
    return `${Math.round(value)}${unit}`;
  };

  const updateLocalGoal = (goalType: 'calories' | 'protein' | 'carbs' | 'fat', value: string) => {
    const numValue = parseInt(value) || 0;
    setLocalGoals(prev => ({ ...prev, [goalType]: numValue }));
  };

  const saveGoalsToDatabase = async () => {
    try {
      await updateProfile({
        daily_calorie_goal: localGoals.calories,
        daily_protein_goal: localGoals.protein,
        daily_carb_goal: localGoals.carbs,
        daily_fat_goal: localGoals.fat
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to save goals. Please try again.');
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView 
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <SafeAreaView style={styles.modalContainer}>
        <View style={styles.header}>
          <BrutalistText
            variant="title3"
            fontFamily="system"
            weight="black"
            color="grey800"
          >
            ACCOUNT
          </BrutalistText>
          
          <TouchableOpacity 
            style={styles.closeButton}
            onPress={onClose}
            activeOpacity={0.7}
          >
            <BrutalistText
              variant="callout"
              weight="medium"
              color="grey600"
              fontFamily="mono"
            >
              DONE
            </BrutalistText>
          </TouchableOpacity>
        </View>

        <ScrollView 
          style={styles.content} 
          showsVerticalScrollIndicator={false}
        >
          {/* Profile Section */}
          <View style={styles.profileSection}>
            <View style={styles.largeAvatar}>
              <BrutalistText
                variant="title1"
                weight="bold"
                fontFamily="system"
                color="white"
              >
                {getInitials()}
              </BrutalistText>
            </View>

            <View style={styles.userInfo}>
              <BrutalistText
                variant="title3"
                weight="bold"
                color="grey900"
                style={styles.userName}
              >
                {profile?.full_name || profile?.username || 'User'}
              </BrutalistText>
              
              <BrutalistText
                variant="callout"
                color="grey600"
                fontFamily="mono"
              >
                {user?.email}
              </BrutalistText>
            </View>
          </View>

          {/* Daily Goals Section - Clean like Today screen */}
          <View style={styles.section}>
            <View style={styles.sectionHeaderContainer}>
              <BrutalistText
                variant="caption1"
                weight="medium"
                color="grey700"
                fontFamily="mono"
                style={styles.sectionHeader}
              >
                DAILY NUTRITION GOALS
              </BrutalistText>
              <BrutalistText
                variant="caption2"
                color="grey500"
                fontFamily="mono"
                style={styles.editHint}
              >
                TAP TO EDIT
              </BrutalistText>
            </View>

            <View style={styles.goalsContainer}>
              <View style={styles.goalRow}>
                <BrutalistText variant="callout" color="grey600">Calories</BrutalistText>
                <TextInput
                  style={styles.goalValue}
                  value={localGoals.calories.toString()}
                  onChangeText={(text) => updateLocalGoal('calories', text)}
                  onBlur={saveGoalsToDatabase}
                  keyboardType="numeric"
                  returnKeyType="done"
                />
              </View>
              
              <View style={styles.goalRow}>
                <BrutalistText variant="callout" color="grey600">Protein</BrutalistText>
                <View style={styles.goalWithUnit}>
                  <TextInput
                    style={styles.goalValue}
                    value={localGoals.protein.toString()}
                    onChangeText={(text) => updateLocalGoal('protein', text)}
                    onBlur={saveGoalsToDatabase}
                    keyboardType="numeric"
                    returnKeyType="done"
                  />
                  <BrutalistText style={styles.unitText}>g</BrutalistText>
                </View>
              </View>
              
              <View style={styles.goalRow}>
                <BrutalistText variant="callout" color="grey600">Carbs</BrutalistText>
                <View style={styles.goalWithUnit}>
                  <TextInput
                    style={styles.goalValue}
                    value={localGoals.carbs.toString()}
                    onChangeText={(text) => updateLocalGoal('carbs', text)}
                    onBlur={saveGoalsToDatabase}
                    keyboardType="numeric"
                    returnKeyType="done"
                  />
                  <BrutalistText style={styles.unitText}>g</BrutalistText>
                </View>
              </View>
              
              <View style={styles.goalRow}>
                <BrutalistText variant="callout" color="grey600">Fat</BrutalistText>
                <View style={styles.goalWithUnit}>
                  <TextInput
                    style={styles.goalValue}
                    value={localGoals.fat.toString()}
                    onChangeText={(text) => updateLocalGoal('fat', text)}
                    onBlur={saveGoalsToDatabase}
                    keyboardType="numeric"
                    returnKeyType="done"
                  />
                  <BrutalistText style={styles.unitText}>g</BrutalistText>
                </View>
              </View>
            </View>
          </View>

          {/* Account Section */}
          <View style={styles.section}>
            <BrutalistText
              variant="caption1"
              weight="medium"
              color="grey700"
              fontFamily="mono"
              style={styles.sectionHeader}
            >
              ACCOUNT DETAILS
            </BrutalistText>

            <View style={styles.accountInfo}>
              <View style={styles.accountRow}>
                <BrutalistText
                  variant="callout"
                  color="grey600"
                >
                  Member since
                </BrutalistText>
                <BrutalistText
                  variant="callout"
                  weight="medium"
                  color="grey800"
                  fontFamily="mono"
                >
                  {profile?.created_at 
                    ? new Date(profile.created_at).toLocaleDateString()
                    : 'N/A'
                  }
                </BrutalistText>
              </View>

              <View style={styles.accountRow}>
                <BrutalistText
                  variant="callout"
                  color="grey600"
                >
                  Account ID
                </BrutalistText>
                <BrutalistText
                  variant="caption1"
                  weight="medium"
                  color="grey700"
                  fontFamily="mono"
                >
                  {user?.id?.slice(0, 8)}...
                </BrutalistText>
              </View>
            </View>
          </View>
        </ScrollView>

        {/* Logout Button - Subtle */}
        <View style={styles.footer}>
          <TouchableOpacity 
            style={styles.logoutButton}
            onPress={handleLogout}
            activeOpacity={0.7}
          >
            <BrutalistText
              variant="callout"
              weight="medium"
              color="grey600"
              fontFamily="mono"
            >
              Sign out
            </BrutalistText>
          </TouchableOpacity>
        </View>
        </SafeAreaView>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BrutalistTheme.colors.white,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: BrutalistTheme.colors.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingTop: BrutalistTheme.spacing.md,
    paddingBottom: BrutalistTheme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey200,
  },
  closeButton: {
    position: 'absolute',
    right: BrutalistTheme.spacing.lg,
  },
  content: {
    flex: 1,
    paddingHorizontal: BrutalistTheme.spacing.lg,
  },
  
  // Profile Section
  profileSection: {
    alignItems: 'center',
    paddingVertical: BrutalistTheme.spacing.xl,
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey200,
    marginBottom: BrutalistTheme.spacing.lg,
  },
  largeAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: BrutalistTheme.colors.accent1,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: BrutalistTheme.borderWidth.medium,
    borderColor: BrutalistTheme.colors.grey300,
    marginBottom: BrutalistTheme.spacing.md,
  },
  userInfo: {
    alignItems: 'center',
    gap: BrutalistTheme.spacing.xxs,
  },
  userName: {
    textAlign: 'center',
  },

  // Sections
  section: {
    marginBottom: BrutalistTheme.spacing.xl,
  },
  sectionHeaderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: BrutalistTheme.spacing.md,
  },
  sectionHeader: {
    letterSpacing: 1,
  },
  editHint: {
    letterSpacing: 0.5,
    opacity: 0.7,
  },

  // Goals Container - Clean like History screen
  goalsContainer: {
    gap: 0,
  },
  goalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: BrutalistTheme.spacing.xs,
    paddingHorizontal: BrutalistTheme.spacing.md,
    borderBottomWidth: BrutalistTheme.borderWidth.hairline,
    borderBottomColor: BrutalistTheme.colors.grey300,
  },
  goalValue: {
    fontSize: BrutalistTheme.fontSizes.title3,
    fontWeight: 'bold',
    fontFamily: BrutalistTheme.fonts.mono,
    color: BrutalistTheme.colors.accent1,
    backgroundColor: 'transparent',
    borderWidth: 0,
    padding: 0,
    textAlign: 'right',
    minWidth: 40,
  },
  goalWithUnit: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  unitText: {
    fontSize: BrutalistTheme.fontSizes.title3,
    fontWeight: 'bold',
    fontFamily: BrutalistTheme.fonts.mono,
    color: BrutalistTheme.colors.accent1,
  },


  // Account Info
  accountInfo: {
    gap: BrutalistTheme.spacing.sm,
  },
  accountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: BrutalistTheme.spacing.xs,
    borderBottomWidth: BrutalistTheme.borderWidth.hairline,
    borderBottomColor: BrutalistTheme.colors.grey300,
  },

  // Footer
  footer: {
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingTop: BrutalistTheme.spacing.md,
    paddingBottom: BrutalistTheme.spacing.lg,
    borderTopWidth: 1,
    borderTopColor: BrutalistTheme.colors.grey200,
  },
  logoutButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: BrutalistTheme.spacing.md,
    paddingHorizontal: BrutalistTheme.spacing.lg,
  },
});