import React, { useState, useEffect, useRef } from 'react';
import {
  Modal,
  View,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TextInput,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { BrutalistText } from './BrutalistText';
import { BrutalistTheme } from '../theme/colors';
import { FoodItem, LoggedFood } from '../types/food';
import { foodSearchService } from '../services/foodSearchService';

interface FoodSearchModalProps {
  visible: boolean;
  mealType: LoggedFood['meal_type'];
  onAddFood: (food: LoggedFood) => void;
  onCancel: () => void;
}

export const FoodSearchModal: React.FC<FoodSearchModalProps> = ({
  visible,
  mealType,
  onAddFood,
  onCancel,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<FoodItem[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedFood, setSelectedFood] = useState<FoodItem | null>(null);
  const [quantity, setQuantity] = useState('1');
  const [isAdding, setIsAdding] = useState(false);
  const [editingNutrition, setEditingNutrition] = useState<string | null>(null);
  const [editedNutritionValues, setEditedNutritionValues] = useState<Map<string, Partial<FoodItem>>>(new Map());
  
  const searchTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const searchInputRef = useRef<TextInput>(null);

  // Auto-focus search input when modal opens
  useEffect(() => {
    if (visible) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 300);
    } else {
      // Reset state when modal closes
      setSearchQuery('');
      setSearchResults([]);
      setSelectedFood(null);
      setQuantity('1');
      setIsSearching(false);
    }
  }, [visible]);

  // Debounced search
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
      searchTimeoutRef.current = undefined;
    }

    if (searchQuery.length >= 2) {
      setIsSearching(true);
      searchTimeoutRef.current = setTimeout(async () => {
        try {
          const results = await foodSearchService.searchFoods(searchQuery, 20);
          setSearchResults(results);
        } catch (error) {
          console.error('Search error:', error);
          Alert.alert(
            'Search Error', 
            `Failed to search foods: ${error instanceof Error ? error.message : 'Unknown error'}`
          );
          setSearchResults([]);
        } finally {
          setIsSearching(false);
        }
      }, 300);
    } else {
      setSearchResults([]);
      setIsSearching(false);
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
        searchTimeoutRef.current = undefined;
      }
    };
  }, [searchQuery]);

  const handleFoodSelect = (food: FoodItem) => {
    setSelectedFood(food);
    setQuantity('1');
    // Reset edited nutrition when selecting a new food
    setEditedNutritionValues(new Map());
    setEditingNutrition(null);
  };

  const handleBackToSearch = () => {
    setSelectedFood(null);
    setQuantity('1');
    setEditedNutritionValues(new Map());
    setEditingNutrition(null);
  };

  const handleAddFood = async () => {
    if (!selectedFood || isAdding) return;

    const parsedQuantity = parseFloat(quantity);
    if (isNaN(parsedQuantity) || parsedQuantity <= 0) {
      Alert.alert('Invalid Quantity', 'Please enter a valid quantity greater than 0');
      return;
    }

    setIsAdding(true);

    try {
      const finalFood = getEditedFood();
      const loggedFood: LoggedFood = {
        ...finalFood,
        quantity: parsedQuantity,
        meal_type: mealType,
        logged_at: new Date().toISOString(),
      };

      await onAddFood(loggedFood);
      onCancel(); // Close modal on success
    } catch (error) {
      console.error('Error adding food:', error);
      Alert.alert('Error', 'Failed to add food item. Please try again.');
    } finally {
      setIsAdding(false);
    }
  };

  const formatNutritionValue = (value: number): string => {
    const result = value.toFixed(1);
    return result.endsWith('.0') ? result.slice(0, -2) : result;
  };

  const updateNutritionValue = (field: keyof FoodItem, value: number) => {
    if (!selectedFood) return;
    
    setEditedNutritionValues(prev => {
      const newMap = new Map(prev);
      const current = newMap.get(selectedFood.id) || {};
      newMap.set(selectedFood.id, { ...current, [field]: value });
      return newMap;
    });
  };

  const getNutritionValue = (field: keyof FoodItem): number => {
    if (!selectedFood) return 0;
    const edited = editedNutritionValues.get(selectedFood.id)?.[field];
    return edited !== undefined ? edited as number : (selectedFood[field] as number || 0);
  };

  const isNutritionEdited = (field: keyof FoodItem): boolean => {
    if (!selectedFood) return false;
    return editedNutritionValues.get(selectedFood.id)?.[field] !== undefined;
  };

  const getEditedFood = (): FoodItem => {
    if (!selectedFood) return selectedFood!;
    const editedValues = editedNutritionValues.get(selectedFood.id) || {};
    return { ...selectedFood, ...editedValues };
  };

  const renderSearchResults = () => (
    <View style={styles.searchResultsContainer}>
      <BrutalistText
        variant="caption1"
        weight="medium"
        fontFamily="mono"
        color="grey700"
        style={styles.resultsHeader}
      >
        {isSearching ? 'SEARCHING...' : `${searchResults.length} RESULTS`}
      </BrutalistText>

      <ScrollView
        style={styles.resultsList}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {isSearching && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator
              size="small"
              color={BrutalistTheme.colors.grey600}
            />
          </View>
        )}

        {!isSearching && searchResults.length === 0 && searchQuery.length >= 2 && (
          <View style={styles.emptyState}>
            <BrutalistText
              variant="body"
              color="grey600"
              fontFamily="serif"
              style={styles.emptyMessage}
            >
              No foods found for "{searchQuery}"
            </BrutalistText>
            <BrutalistText
              variant="caption1"
              color="grey500"
              fontFamily="serif"
              style={styles.emptySubMessage}
            >
              Try a different search term
            </BrutalistText>
          </View>
        )}

        {!isSearching && searchResults.map((food, index) => (
          <TouchableOpacity
            key={`${food.id}-${index}`}
            style={styles.foodResultItem}
            onPress={() => handleFoodSelect(food)}
            activeOpacity={0.7}
          >
            <View style={styles.foodResultContent}>
              <View style={styles.foodResultMain}>
                <BrutalistText
                  variant="callout"
                  weight="medium"
                  color="black"
                >
                  {food.name}
                </BrutalistText>
                <BrutalistText
                  variant="caption1"
                  weight="medium"
                  color="grey600"
                  fontFamily="mono"
                >
                  {food.serving_size} • {food.calories} cal
                </BrutalistText>
              </View>
              <BrutalistText
                variant="caption1"
                weight="medium"
                color="grey500"
                fontFamily="mono"
              >
                +
              </BrutalistText>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderFoodDetails = () => (
    <View style={styles.foodDetailsContainer}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={handleBackToSearch}
        activeOpacity={0.7}
      >
        <BrutalistText
          variant="caption1"
          weight="bold"
          fontFamily="mono"
          color="grey700"
        >
          ← BACK TO SEARCH
        </BrutalistText>
      </TouchableOpacity>

      <ScrollView
        style={styles.detailsScrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.foodDetailsContent}>
          <View style={styles.foodHeader}>
            <BrutalistText
              variant="title3"
              weight="semibold"
              color="black"
              style={styles.foodName}
            >
              {selectedFood!.name}
            </BrutalistText>
            <BrutalistText
              variant="caption1"
              weight="medium"
              color="grey600"
              fontFamily="mono"
            >
              per {selectedFood!.serving_size}
            </BrutalistText>
          </View>

          {/* Quantity Input */}
          <View style={styles.quantitySection}>
            <BrutalistText
              variant="caption1"
              weight="medium"
              fontFamily="mono"
              color="grey700"
              style={styles.sectionLabel}
            >
              QUANTITY
            </BrutalistText>
            <View style={styles.quantityInputContainer}>
              <TextInput
                style={styles.quantityInput}
                value={quantity}
                onChangeText={setQuantity}
                keyboardType="numeric"
                placeholder="1"
                placeholderTextColor={BrutalistTheme.colors.grey500}
                selectTextOnFocus
              />
              <BrutalistText
                variant="body"
                weight="medium"
                color="grey700"
                fontFamily="mono"
              >
                × {selectedFood!.serving_size}
              </BrutalistText>
            </View>
          </View>

          {/* Total Nutrition */}
          <View style={styles.nutritionSection}>
            <BrutalistText
              variant="caption1"
              weight="medium"
              fontFamily="mono"
              color="grey700"
              style={styles.sectionLabel}
            >
              TOTAL NUTRITION ({quantity || '1'}×)
            </BrutalistText>

            <View style={styles.nutritionGrid}>
              <View style={styles.macroItem}>
                {editingNutrition === 'calories' ? (
                  <TextInput
                    style={styles.nutritionInput}
                    value={getNutritionValue('calories').toString()}
                    onChangeText={text => {
                      const num = parseFloat(text) || 0;
                      updateNutritionValue('calories', num);
                    }}
                    onBlur={() => setEditingNutrition(null)}
                    onSubmitEditing={() => setEditingNutrition(null)}
                    keyboardType="numeric"
                    selectTextOnFocus
                    autoFocus
                    maxLength={5}
                  />
                ) : (
                  <TouchableOpacity
                    onPress={e => {
                      e.stopPropagation();
                      setEditingNutrition('calories');
                    }}
                    style={styles.nutritionButton}
                    activeOpacity={0.6}
                  >
                    <BrutalistText
                      variant="title3"
                      weight="bold"
                      fontFamily="mono"
                      color="accent1"
                    >
                      {Math.round(getNutritionValue('calories') * (parseFloat(quantity) || 1))}
                    </BrutalistText>
                    {isNutritionEdited('calories') && (
                      <View style={styles.editedIndicator} />
                    )}
                  </TouchableOpacity>
                )}
                <BrutalistText
                  variant="caption2"
                  weight="medium"
                  color="grey600"
                >
                  CALORIES
                </BrutalistText>
              </View>

              <View style={styles.macroItem}>
                {editingNutrition === 'protein' ? (
                  <TextInput
                    style={styles.nutritionInput}
                    value={getNutritionValue('protein').toString()}
                    onChangeText={text => {
                      const num = parseFloat(text) || 0;
                      updateNutritionValue('protein', num);
                    }}
                    onBlur={() => setEditingNutrition(null)}
                    onSubmitEditing={() => setEditingNutrition(null)}
                    keyboardType="numeric"
                    selectTextOnFocus
                    autoFocus
                    maxLength={5}
                  />
                ) : (
                  <TouchableOpacity
                    onPress={e => {
                      e.stopPropagation();
                      setEditingNutrition('protein');
                    }}
                    style={styles.nutritionButton}
                    activeOpacity={0.6}
                  >
                    <BrutalistText
                      variant="callout"
                      weight="semibold"
                      fontFamily="mono"
                      color="black"
                    >
                      {formatNutritionValue(getNutritionValue('protein') * (parseFloat(quantity) || 1))}g
                    </BrutalistText>
                    {isNutritionEdited('protein') && (
                      <View style={styles.editedIndicator} />
                    )}
                  </TouchableOpacity>
                )}
                <BrutalistText
                  variant="caption2"
                  weight="medium"
                  color="grey600"
                >
                  PROTEIN
                </BrutalistText>
              </View>

              <View style={styles.macroItem}>
                {editingNutrition === 'carbs' ? (
                  <TextInput
                    style={styles.nutritionInput}
                    value={getNutritionValue('carbs').toString()}
                    onChangeText={text => {
                      const num = parseFloat(text) || 0;
                      updateNutritionValue('carbs', num);
                    }}
                    onBlur={() => setEditingNutrition(null)}
                    onSubmitEditing={() => setEditingNutrition(null)}
                    keyboardType="numeric"
                    selectTextOnFocus
                    autoFocus
                    maxLength={5}
                  />
                ) : (
                  <TouchableOpacity
                    onPress={e => {
                      e.stopPropagation();
                      setEditingNutrition('carbs');
                    }}
                    style={styles.nutritionButton}
                    activeOpacity={0.6}
                  >
                    <BrutalistText
                      variant="callout"
                      weight="semibold"
                      fontFamily="mono"
                      color="black"
                    >
                      {formatNutritionValue(getNutritionValue('carbs') * (parseFloat(quantity) || 1))}g
                    </BrutalistText>
                    {isNutritionEdited('carbs') && (
                      <View style={styles.editedIndicator} />
                    )}
                  </TouchableOpacity>
                )}
                <BrutalistText
                  variant="caption2"
                  weight="medium"
                  color="grey600"
                >
                  CARBS
                </BrutalistText>
              </View>

              <View style={styles.macroItem}>
                {editingNutrition === 'fat' ? (
                  <TextInput
                    style={styles.nutritionInput}
                    value={getNutritionValue('fat').toString()}
                    onChangeText={text => {
                      const num = parseFloat(text) || 0;
                      updateNutritionValue('fat', num);
                    }}
                    onBlur={() => setEditingNutrition(null)}
                    onSubmitEditing={() => setEditingNutrition(null)}
                    keyboardType="numeric"
                    selectTextOnFocus
                    autoFocus
                    maxLength={5}
                  />
                ) : (
                  <TouchableOpacity
                    onPress={e => {
                      e.stopPropagation();
                      setEditingNutrition('fat');
                    }}
                    style={styles.nutritionButton}
                    activeOpacity={0.6}
                  >
                    <BrutalistText
                      variant="callout"
                      weight="semibold"
                      fontFamily="mono"
                      color="black"
                    >
                      {formatNutritionValue(getNutritionValue('fat') * (parseFloat(quantity) || 1))}g
                    </BrutalistText>
                    {isNutritionEdited('fat') && (
                      <View style={styles.editedIndicator} />
                    )}
                  </TouchableOpacity>
                )}
                <BrutalistText
                  variant="caption2"
                  weight="medium"
                  color="grey600"
                >
                  FAT
                </BrutalistText>
              </View>
            </View>

            {/* Secondary nutrition if available */}
            {(getNutritionValue('fiber') > 0 || getNutritionValue('sugar') > 0 || getNutritionValue('sodium') > 0 || getNutritionValue('cholesterol') > 0 || 
              isNutritionEdited('fiber') || isNutritionEdited('sugar') || isNutritionEdited('sodium') || isNutritionEdited('cholesterol')) && (
              <View style={styles.nutritionGrid}>
                {(getNutritionValue('fiber') > 0 || isNutritionEdited('fiber')) && (
                  <View style={styles.macroItem}>
                    {editingNutrition === 'fiber' ? (
                      <TextInput
                        style={styles.nutritionInput}
                        value={getNutritionValue('fiber').toString()}
                        onChangeText={text => {
                          const num = parseFloat(text) || 0;
                          updateNutritionValue('fiber', num);
                        }}
                        onBlur={() => setEditingNutrition(null)}
                        onSubmitEditing={() => setEditingNutrition(null)}
                        keyboardType="numeric"
                        selectTextOnFocus
                        autoFocus
                        maxLength={5}
                      />
                    ) : (
                      <TouchableOpacity
                        onPress={e => {
                          e.stopPropagation();
                          setEditingNutrition('fiber');
                        }}
                        style={styles.nutritionButton}
                        activeOpacity={0.6}
                      >
                        <BrutalistText
                          variant="callout"
                          weight="semibold"
                          fontFamily="mono"
                          color="black"
                        >
                          {formatNutritionValue(getNutritionValue('fiber') * (parseFloat(quantity) || 1))}g
                        </BrutalistText>
                        {isNutritionEdited('fiber') && (
                          <View style={styles.editedIndicator} />
                        )}
                      </TouchableOpacity>
                    )}
                    <BrutalistText
                      variant="caption2"
                      weight="medium"
                      color="grey600"
                    >
                      FIBER
                    </BrutalistText>
                  </View>
                )}

                {(getNutritionValue('sugar') > 0 || isNutritionEdited('sugar')) && (
                  <View style={styles.macroItem}>
                    {editingNutrition === 'sugar' ? (
                      <TextInput
                        style={styles.nutritionInput}
                        value={getNutritionValue('sugar').toString()}
                        onChangeText={text => {
                          const num = parseFloat(text) || 0;
                          updateNutritionValue('sugar', num);
                        }}
                        onBlur={() => setEditingNutrition(null)}
                        onSubmitEditing={() => setEditingNutrition(null)}
                        keyboardType="numeric"
                        selectTextOnFocus
                        autoFocus
                        maxLength={5}
                      />
                    ) : (
                      <TouchableOpacity
                        onPress={e => {
                          e.stopPropagation();
                          setEditingNutrition('sugar');
                        }}
                        style={styles.nutritionButton}
                        activeOpacity={0.6}
                      >
                        <BrutalistText
                          variant="callout"
                          weight="semibold"
                          fontFamily="mono"
                          color="black"
                        >
                          {formatNutritionValue(getNutritionValue('sugar') * (parseFloat(quantity) || 1))}g
                        </BrutalistText>
                        {isNutritionEdited('sugar') && (
                          <View style={styles.editedIndicator} />
                        )}
                      </TouchableOpacity>
                    )}
                    <BrutalistText
                      variant="caption2"
                      weight="medium"
                      color="grey600"
                    >
                      SUGAR
                    </BrutalistText>
                  </View>
                )}

                {(getNutritionValue('sodium') > 0 || isNutritionEdited('sodium')) && (
                  <View style={styles.macroItem}>
                    {editingNutrition === 'sodium' ? (
                      <TextInput
                        style={styles.nutritionInput}
                        value={getNutritionValue('sodium').toString()}
                        onChangeText={text => {
                          const num = parseFloat(text) || 0;
                          updateNutritionValue('sodium', num);
                        }}
                        onBlur={() => setEditingNutrition(null)}
                        onSubmitEditing={() => setEditingNutrition(null)}
                        keyboardType="numeric"
                        selectTextOnFocus
                        autoFocus
                        maxLength={5}
                      />
                    ) : (
                      <TouchableOpacity
                        onPress={e => {
                          e.stopPropagation();
                          setEditingNutrition('sodium');
                        }}
                        style={styles.nutritionButton}
                        activeOpacity={0.6}
                      >
                        <BrutalistText
                          variant="callout"
                          weight="semibold"
                          fontFamily="mono"
                          color="black"
                        >
                          {Math.round(getNutritionValue('sodium') * (parseFloat(quantity) || 1))}mg
                        </BrutalistText>
                        {isNutritionEdited('sodium') && (
                          <View style={styles.editedIndicator} />
                        )}
                      </TouchableOpacity>
                    )}
                    <BrutalistText
                      variant="caption2"
                      weight="medium"
                      color="grey600"
                    >
                      SODIUM
                    </BrutalistText>
                  </View>
                )}

                {(getNutritionValue('cholesterol') > 0 || isNutritionEdited('cholesterol')) && (
                  <View style={styles.macroItem}>
                    {editingNutrition === 'cholesterol' ? (
                      <TextInput
                        style={styles.nutritionInput}
                        value={getNutritionValue('cholesterol').toString()}
                        onChangeText={text => {
                          const num = parseFloat(text) || 0;
                          updateNutritionValue('cholesterol', num);
                        }}
                        onBlur={() => setEditingNutrition(null)}
                        onSubmitEditing={() => setEditingNutrition(null)}
                        keyboardType="numeric"
                        selectTextOnFocus
                        autoFocus
                        maxLength={5}
                      />
                    ) : (
                      <TouchableOpacity
                        onPress={e => {
                          e.stopPropagation();
                          setEditingNutrition('cholesterol');
                        }}
                        style={styles.nutritionButton}
                        activeOpacity={0.6}
                      >
                        <BrutalistText
                          variant="callout"
                          weight="semibold"
                          fontFamily="mono"
                          color="black"
                        >
                          {Math.round(getNutritionValue('cholesterol') * (parseFloat(quantity) || 1))}mg
                        </BrutalistText>
                        {isNutritionEdited('cholesterol') && (
                          <View style={styles.editedIndicator} />
                        )}
                      </TouchableOpacity>
                    )}
                    <BrutalistText
                      variant="caption2"
                      weight="medium"
                      color="grey600"
                    >
                      CHOL
                    </BrutalistText>
                  </View>
                )}
              </View>
            )}
          </View>
        </View>
      </ScrollView>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onCancel}
    >
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.container}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity
              style={[styles.closeButton, (isSearching || isAdding) && styles.disabledButton]}
              onPress={(isSearching || isAdding) ? undefined : onCancel}
              activeOpacity={(isSearching || isAdding) ? 1 : 0.7}
            >
              <BrutalistText
                variant="callout"
                weight="bold"
                fontFamily="mono"
                color={(isSearching || isAdding) ? 'grey400' : 'grey700'}
              >
                ✕
              </BrutalistText>
            </TouchableOpacity>

            <View style={styles.headerContent}>
              <BrutalistText
                variant="title3"
                weight="black"
                fontFamily="system"
                color="grey800"
                style={styles.headerTitle}
              >
                {selectedFood ? 'ADD ITEM' : 'SEARCH FOODS'}
              </BrutalistText>
              <BrutalistText
                variant="caption1"
                weight="medium"
                fontFamily="mono"
                color="grey600"
              >
                {mealType.toUpperCase()}
              </BrutalistText>
            </View>

            <View style={styles.headerSpacer} />
          </View>

          {/* Search Input - Only show when not in food details */}
          {!selectedFood && (
            <View style={styles.searchContainer}>
              <TextInput
                ref={searchInputRef}
                style={styles.searchInput}
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholder="Search for food..."
                placeholderTextColor={BrutalistTheme.colors.grey500}
                autoCapitalize="none"
                autoCorrect={false}
                returnKeyType="search"
              />
            </View>
          )}

          {/* Content */}
          <View style={styles.content}>
            {selectedFood ? renderFoodDetails() : renderSearchResults()}
          </View>

          {/* Action Buttons - Only show in food details */}
          {selectedFood && (
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={styles.cancelAction}
                onPress={handleBackToSearch}
                activeOpacity={0.7}
                disabled={isAdding}
              >
                <BrutalistText
                  variant="body"
                  weight="medium"
                  color={isAdding ? 'grey400' : 'grey700'}
                  fontFamily="system"
                >
                  Back
                </BrutalistText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.addAction, isAdding && styles.disabledAction]}
                onPress={handleAddFood}
                activeOpacity={0.7}
                disabled={isAdding}
              >
                <View style={styles.addButtonContent}>
                  {isAdding && (
                    <ActivityIndicator
                      size="small"
                      color={BrutalistTheme.colors.grey600}
                      style={styles.loadingIndicator}
                    />
                  )}
                  <BrutalistText
                    variant="body"
                    weight="medium"
                    color={isAdding ? 'grey500' : 'black'}
                    fontFamily="system"
                  >
                    {isAdding ? 'Adding...' : 'Add to Meal'}
                  </BrutalistText>
                </View>
                <View
                  style={[
                    styles.addActionUnderline,
                    isAdding && styles.disabledUnderline,
                  ]}
                />
              </TouchableOpacity>
            </View>
          )}
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BrutalistTheme.colors.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingTop: BrutalistTheme.spacing.lg,
    paddingBottom: BrutalistTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey200,
  },
  closeButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    opacity: 0.5,
  },
  headerContent: {
    alignItems: 'center',
    gap: 4,
  },
  headerTitle: {
    letterSpacing: 1,
  },
  headerSpacer: {
    width: 44,
  },
  searchContainer: {
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingVertical: BrutalistTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey200,
  },
  searchInput: {
    borderWidth: 2,
    borderColor: BrutalistTheme.colors.grey300,
    borderRadius: BrutalistTheme.borderRadius.sm,
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingVertical: BrutalistTheme.spacing.sm,
    fontSize: BrutalistTheme.fontSizes.body,
    fontFamily: BrutalistTheme.fonts.mono,
    color: BrutalistTheme.colors.grey800,
    backgroundColor: BrutalistTheme.colors.white,
  },
  content: {
    flex: 1,
  },

  // Search Results
  searchResultsContainer: {
    flex: 1,
  },
  resultsHeader: {
    textAlign: 'center',
    letterSpacing: 1,
    paddingVertical: BrutalistTheme.spacing.sm,
    backgroundColor: BrutalistTheme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey200,
  },
  resultsList: {
    flex: 1,
  },
  loadingContainer: {
    paddingVertical: BrutalistTheme.spacing.xl,
    alignItems: 'center',
  },
  emptyState: {
    paddingVertical: BrutalistTheme.spacing.xl,
    paddingHorizontal: BrutalistTheme.spacing.lg,
    alignItems: 'center',
    gap: BrutalistTheme.spacing.sm,
  },
  emptyMessage: {
    textAlign: 'center',
  },
  emptySubMessage: {
    textAlign: 'center',
  },
  foodResultItem: {
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingVertical: BrutalistTheme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey200,
  },
  foodResultContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  foodResultMain: {
    flex: 1,
    gap: 4,
  },

  // Food Details
  foodDetailsContainer: {
    flex: 1,
  },
  backButton: {
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingVertical: BrutalistTheme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey200,
  },
  detailsScrollView: {
    flex: 1,
  },
  foodDetailsContent: {
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingTop: BrutalistTheme.spacing.md,
    paddingBottom: BrutalistTheme.spacing.lg,
    gap: BrutalistTheme.spacing.lg,
  },
  foodHeader: {
    alignItems: 'center',
    gap: BrutalistTheme.spacing.xs,
    paddingBottom: BrutalistTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey200,
  },
  foodName: {
    textAlign: 'center',
  },

  // Quantity Section
  quantitySection: {
    alignItems: 'center',
    gap: BrutalistTheme.spacing.sm,
  },
  sectionLabel: {
    textAlign: 'center',
    letterSpacing: 1,
  },
  quantityInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BrutalistTheme.spacing.sm,
  },
  quantityInput: {
    borderWidth: 2,
    borderColor: BrutalistTheme.colors.grey300,
    borderRadius: BrutalistTheme.borderRadius.sm,
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingVertical: BrutalistTheme.spacing.sm,
    fontSize: BrutalistTheme.fontSizes.callout,
    fontFamily: BrutalistTheme.fonts.mono,
    color: BrutalistTheme.colors.grey800,
    backgroundColor: BrutalistTheme.colors.white,
    textAlign: 'center',
    width: 80,
  },

  // Nutrition Section
  nutritionSection: {
    alignItems: 'center',
    gap: BrutalistTheme.spacing.sm,
  },
  nutritionGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: BrutalistTheme.spacing.sm,
  },
  macroItem: {
    alignItems: 'center',
    flex: 1,
    gap: 2,
  },
  
  nutritionButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: BrutalistTheme.spacing.xs,
    paddingVertical: 2,
    position: 'relative',
  },
  
  nutritionInput: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'SF Mono',
    textAlign: 'center',
    minWidth: 40,
    paddingHorizontal: BrutalistTheme.spacing.xs,
    paddingVertical: 2,
    borderWidth: 0,
    backgroundColor: 'transparent',
    color: BrutalistTheme.colors.black,
  },
  
  editedIndicator: {
    position: 'absolute',
    bottom: -1,
    left: '50%',
    transform: [{ translateX: -6 }],
    width: 12,
    height: 1,
    backgroundColor: BrutalistTheme.colors.success,
  },

  // Action Buttons
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: BrutalistTheme.spacing.lg,
    borderTopWidth: 1,
    borderTopColor: BrutalistTheme.colors.grey200,
  },
  cancelAction: {
    paddingVertical: BrutalistTheme.spacing.xs,
    paddingHorizontal: BrutalistTheme.spacing.xs,
  },
  addAction: {
    alignItems: 'flex-end',
    paddingVertical: BrutalistTheme.spacing.xs,
  },
  addButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BrutalistTheme.spacing.xs,
  },
  loadingIndicator: {
    marginRight: 2,
  },
  addActionUnderline: {
    height: 2,
    backgroundColor: BrutalistTheme.colors.grey700,
    marginTop: 2,
    width: '100%',
  },
  disabledAction: {
    opacity: 0.5,
  },
  disabledUnderline: {
    backgroundColor: BrutalistTheme.colors.grey400,
  },
});