import React from 'react';
import { Text, StyleSheet, TextStyle } from 'react-native';
import { BrutalistTheme } from '../theme/colors';

interface BrutalistTextProps {
  children: React.ReactNode;
  variant?: 'largeTitle' | 'title1' | 'title2' | 'title3' | 'headline' | 'body' | 'callout' | 'subheadline' | 'footnote' | 'caption1' | 'caption2' | 'brutalistHero' | 'brutalistTitle' | 'brutalistHeadline' | 'brutalistBody';
  color?: keyof typeof BrutalistTheme.colors;
  weight?: 'regular' | 'medium' | 'semibold' | 'bold' | 'heavy' | 'black';
  fontFamily?: 'system' | 'serif' | 'mono';
  style?: TextStyle;
}

export const BrutalistText: React.FC<BrutalistTextProps> = ({
  children,
  variant = 'body',
  color = 'black',
  weight = 'regular',
  fontFamily,
  style,
}) => {
  // Determine font family based on variant and prop
  const getFontFamily = () => {
    if (fontFamily) {
      switch (fontFamily) {
        case 'serif': return BrutalistTheme.fonts.serif;
        case 'mono': return BrutalistTheme.fonts.mono;
        case 'system': return BrutalistTheme.fonts.system;
        default: return BrutalistTheme.fonts.system;
      }
    }
    
    // Auto-assign font family based on variant
    if (variant.startsWith('brutalist')) {
      return BrutalistTheme.fonts.system; // Sans-serif for brutalist elements
    } else if (['body', 'callout', 'subheadline'].includes(variant)) {
      return BrutalistTheme.fonts.serif; // Serif for reading text
    } else {
      return BrutalistTheme.fonts.system; // Sans-serif for UI elements
    }
  };

  const textStyle = [
    styles.base,
    styles[variant],
    styles[weight],
    { 
      color: BrutalistTheme.colors[color],
      fontFamily: getFontFamily(),
    },
    style,
  ];

  return <Text style={textStyle}>{children}</Text>;
};

const styles = StyleSheet.create({
  base: {
    letterSpacing: 0,
  },
  
  // iOS Typography Scale
  largeTitle: {
    fontSize: BrutalistTheme.fontSizes.largeTitle,
    fontWeight: '700',
    lineHeight: 41,
  },
  title1: {
    fontSize: BrutalistTheme.fontSizes.title1,
    fontWeight: '700',
    lineHeight: 34,
  },
  title2: {
    fontSize: BrutalistTheme.fontSizes.title2,
    fontWeight: '700',
    lineHeight: 28,
  },
  title3: {
    fontSize: BrutalistTheme.fontSizes.title3,
    fontWeight: '600',
    lineHeight: 25,
  },
  headline: {
    fontSize: BrutalistTheme.fontSizes.headline,
    fontWeight: '600',
    lineHeight: 22,
  },
  body: {
    fontSize: BrutalistTheme.fontSizes.body,
    fontWeight: '400',
    lineHeight: 22,
  },
  callout: {
    fontSize: BrutalistTheme.fontSizes.callout,
    fontWeight: '400',
    lineHeight: 21,
  },
  subheadline: {
    fontSize: BrutalistTheme.fontSizes.subheadline,
    fontWeight: '400',
    lineHeight: 20,
  },
  footnote: {
    fontSize: BrutalistTheme.fontSizes.footnote,
    fontWeight: '400',
    lineHeight: 18,
  },
  caption1: {
    fontSize: BrutalistTheme.fontSizes.caption1,
    fontWeight: '400',
    lineHeight: 16,
  },
  caption2: {
    fontSize: BrutalistTheme.fontSizes.caption2,
    fontWeight: '400',
    lineHeight: 13,
  },
  
  // Brutalist Typography (uppercase, bold, with letter spacing)
  brutalistHero: {
    fontSize: BrutalistTheme.fontSizes.brutalistHero,
    fontWeight: '900',
    textTransform: 'uppercase',
    letterSpacing: 2,
    lineHeight: 56,
  },
  brutalistTitle: {
    fontSize: BrutalistTheme.fontSizes.brutalistTitle,
    fontWeight: '900',
    textTransform: 'uppercase',
    letterSpacing: 1.5,
    lineHeight: 38,
  },
  brutalistHeadline: {
    fontSize: BrutalistTheme.fontSizes.brutalistHeadline,
    fontWeight: '800',
    textTransform: 'uppercase',
    letterSpacing: 1,
    lineHeight: 30,
  },
  brutalistBody: {
    fontSize: BrutalistTheme.fontSizes.brutalistBody,
    fontWeight: '700',
    letterSpacing: 0.5,
    lineHeight: 24,
  },
  
  // Font Weights (iOS system weights)
  regular: {
    fontWeight: '400',
  },
  medium: {
    fontWeight: '500',
  },
  semibold: {
    fontWeight: '600',
  },
  bold: {
    fontWeight: '700',
  },
  heavy: {
    fontWeight: '800',
  },
  black: {
    fontWeight: '900',
  },
});