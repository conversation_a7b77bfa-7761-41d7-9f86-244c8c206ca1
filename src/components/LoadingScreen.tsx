import React from 'react';
import { View, StyleSheet, SafeAreaView, StatusBar } from 'react-native';
import { BrutalistText } from './BrutalistText';
import { BrutalistTheme } from '../theme/colors';

export const LoadingScreen: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={BrutalistTheme.colors.white}
      />

      <View style={styles.content}>
        <BrutalistText
          variant="brutalistHero"
          fontFamily="system"
          weight="black"
          color="black"
          style={styles.logo}
        >
          KOA
        </BrutalistText>

        <BrutalistText
          variant="body"
          color="grey600"
          style={styles.loadingText}
        >
          Loading...
        </BrutalistText>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BrutalistTheme.colors.white,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: BrutalistTheme.spacing.lg,
  },
  logo: {
    marginBottom: BrutalistTheme.spacing.md,
  },
  loadingText: {
    textAlign: 'center',
  },
});
