import React from 'react'
import {
  TouchableOpacity,
  View,
  StyleSheet,
  Text,
} from 'react-native'
import { BrutalistText } from './BrutalistText'
import { BrutalistTheme } from '../theme/colors'

interface AppleSignInButtonProps {
  onPress: () => void
  disabled?: boolean
  loading?: boolean
}

export const AppleSignInButton: React.FC<AppleSignInButtonProps> = ({
  onPress,
  disabled = false,
  loading = false,
}) => {
  return (
    <TouchableOpacity
      style={[styles.button, disabled && styles.disabled]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      <BrutalistText
        variant="callout"
        weight="bold"
        color="white"
        fontFamily="mono"
      >
        {loading ? 'SIGNING IN...' : 'APPLE'}
      </BrutalistText>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  button: {
    backgroundColor: BrutalistTheme.colors.black,
    borderWidth: BrutalistTheme.borderWidth.medium,
    borderColor: BrutalistTheme.colors.black,
    borderRadius: BrutalistTheme.borderRadius.sm,
    minHeight: 48,
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingVertical: BrutalistTheme.spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    ...BrutalistTheme.shadows.subtle,
  },
  disabled: {
    backgroundColor: BrutalistTheme.colors.grey400,
    borderColor: BrutalistTheme.colors.grey400,
  },
})