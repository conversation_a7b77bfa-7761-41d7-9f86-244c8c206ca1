import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { BrutalistText } from './BrutalistText';
import { BrutalistTheme } from '../theme/colors';
import { useAuth } from '../contexts/AuthContext';

interface AppHeaderProps {
  title: string;
  variant?: 'minimal' | 'standard';
  onProfilePress?: () => void;
}

export const AppHeader: React.FC<AppHeaderProps> = ({ 
  title, 
  variant = 'standard',
  onProfilePress 
}) => {
  const { user, profile } = useAuth();
  
  // Get user initials for profile avatar
  const getInitials = () => {
    if (profile?.full_name) {
      return profile.full_name
        .split(' ')
        .map(name => name.charAt(0).toUpperCase())
        .slice(0, 2)
        .join('');
    }
    if (profile?.username) {
      return profile.username.charAt(0).toUpperCase();
    }
    if (user?.email) {
      return user.email.charAt(0).toUpperCase();
    }
    return '?';
  };

  return (
    <View style={[styles.header, variant === 'minimal' && styles.headerMinimal]}>
      <BrutalistText
        variant="title3"
        fontFamily="system"
        weight="black"
        color="grey800"
      >
        {title}
      </BrutalistText>
      
      {user && onProfilePress && (
        <TouchableOpacity 
          style={styles.profileButton}
          onPress={onProfilePress}
          activeOpacity={0.7}
        >
          <View style={styles.profileAvatar}>
            <BrutalistText
              variant="callout"
              weight="bold"
              fontFamily="system"
              color="white"
            >
              {getInitials()}
            </BrutalistText>
          </View>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    backgroundColor: BrutalistTheme.colors.white,
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingTop: BrutalistTheme.spacing.xxs,
    paddingBottom: BrutalistTheme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey200,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerMinimal: {
    paddingTop: BrutalistTheme.spacing.xxs,
    paddingBottom: BrutalistTheme.spacing.sm,
  },
  profileButton: {
    position: 'absolute',
    right: BrutalistTheme.spacing.lg,
    top: BrutalistTheme.spacing.xxs,
    bottom: BrutalistTheme.spacing.sm,
    justifyContent: 'center',
  },
  profileAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: BrutalistTheme.colors.accent1,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: BrutalistTheme.borderWidth.thin,
    borderColor: BrutalistTheme.colors.grey300,
  },
});