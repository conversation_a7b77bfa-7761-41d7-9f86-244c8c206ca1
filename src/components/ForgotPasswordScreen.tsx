import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  TextInput,
  Alert,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { BrutalistText } from './index';
import { BrutalistTheme } from '../theme/colors';
import { useAuth } from '../contexts/AuthContext';

interface ForgotPasswordScreenProps {
  onBack: () => void;
}

export const ForgotPasswordScreen: React.FC<ForgotPasswordScreenProps> = ({
  onBack,
}) => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isEmailSent, setIsEmailSent] = useState(false);
  const { resetPasswordForEmail } = useAuth();

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleResetPassword = async () => {
    if (!email.trim()) {
      Alert.alert('Error', 'Please enter your email address');
      return;
    }

    if (!validateEmail(email.trim())) {
      Alert.alert('Error', 'Please enter a valid email address');
      return;
    }

    console.log('Starting password reset for email:', email.trim());
    setIsLoading(true);
    
    try {
      const { error } = await resetPasswordForEmail(email.trim());
      console.log('Password reset result:', { error });

      if (error) {
        console.error('Password reset error:', error);
        Alert.alert('Reset Failed', error.message || 'Unknown error occurred');
        setIsLoading(false);
      } else {
        console.log('Password reset email sent successfully');
        setIsEmailSent(true);
        setIsLoading(false);
      }
    } catch (err) {
      console.error('Password reset exception:', err);
      Alert.alert('Reset Failed', 'An unexpected error occurred. Please try again.');
      setIsLoading(false);
    }
  };

  if (isEmailSent) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar
          barStyle="dark-content"
          backgroundColor={BrutalistTheme.colors.white}
        />

        <View style={styles.content}>
          <View style={styles.header}>
            <BrutalistText
              variant="title1"
              fontFamily="system"
              weight="black"
              color="grey800"
            >
              KOA
            </BrutalistText>
            <BrutalistText
              variant="caption1"
              fontFamily="mono"
              weight="medium"
              color="grey600"
              style={styles.subtitle}
            >
              CHECK YOUR EMAIL
            </BrutalistText>
          </View>

          <View style={styles.successContent}>
            <BrutalistText
              variant="body"
              fontFamily="system"
              weight="regular"
              color="grey700"
              style={styles.successMessage}
            >
              We've sent a password reset link to:
            </BrutalistText>
            
            <BrutalistText
              variant="callout"
              fontFamily="system"
              weight="medium"
              color="black"
              style={styles.emailText}
            >
              {email}
            </BrutalistText>

            <BrutalistText
              variant="footnote"
              fontFamily="system"
              weight="regular"
              color="grey600"
              style={styles.instructionText}
            >
              Check your email and click the reset link to create a new password. The link will expire in 1 hour.
            </BrutalistText>
          </View>

          <View style={styles.footer}>
            <TouchableOpacity
              onPress={onBack}
              style={styles.backButton}
              activeOpacity={0.7}
            >
              <BrutalistText
                variant="callout"
                weight="medium"
                color="grey600"
                fontFamily="mono"
              >
                Back to Sign In
              </BrutalistText>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={BrutalistTheme.colors.white}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoid}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.header}>
            <BrutalistText
              variant="title1"
              fontFamily="system"
              weight="black"
              color="grey800"
            >
              KOA
            </BrutalistText>
            <BrutalistText
              variant="caption1"
              fontFamily="mono"
              weight="medium"
              color="grey600"
              style={styles.subtitle}
            >
              RESET YOUR PASSWORD
            </BrutalistText>
          </View>

          <View style={styles.form}>
            <BrutalistText
              variant="body"
              fontFamily="system"
              weight="regular"
              color="grey700"
              style={styles.description}
            >
              Enter your email address and we'll send you a link to reset your password.
            </BrutalistText>

            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={email}
                onChangeText={setEmail}
                placeholder="Email address"
                placeholderTextColor={BrutalistTheme.colors.grey500}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                returnKeyType="go"
                onSubmitEditing={handleResetPassword}
              />
            </View>

            <TouchableOpacity
              style={[
                styles.resetButton,
                isLoading && styles.resetButtonDisabled
              ]}
              onPress={handleResetPassword}
              disabled={isLoading}
              activeOpacity={0.7}
            >
              <BrutalistText
                variant="callout"
                weight="bold"
                color="white"
                fontFamily="system"
              >
                {isLoading ? 'Sending...' : 'Send Reset Link'}
              </BrutalistText>
            </TouchableOpacity>
          </View>

          <View style={styles.footer}>
            <TouchableOpacity
              onPress={onBack}
              style={styles.backButton}
              activeOpacity={0.7}
            >
              <BrutalistText
                variant="callout"
                weight="medium"
                color="grey600"
                fontFamily="mono"
              >
                Back to Sign In
              </BrutalistText>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BrutalistTheme.colors.white,
  },
  keyboardAvoid: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingVertical: BrutalistTheme.spacing.xl,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingVertical: BrutalistTheme.spacing.xl,
  },

  // Header
  header: {
    alignItems: 'center',
    marginBottom: BrutalistTheme.spacing.xl,
    paddingTop: BrutalistTheme.spacing.xxl,
    gap: BrutalistTheme.spacing.xs,
  },
  subtitle: {
    letterSpacing: 1,
  },

  // Form
  form: {
    flex: 1,
    gap: BrutalistTheme.spacing.md,
  },
  description: {
    textAlign: 'center',
    marginBottom: BrutalistTheme.spacing.lg,
    lineHeight: 22,
  },
  inputContainer: {
    // No additional styling needed
  },
  input: {
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey300,
    paddingHorizontal: 0,
    paddingVertical: BrutalistTheme.spacing.md,
    fontSize: BrutalistTheme.fontSizes.body,
    fontFamily: BrutalistTheme.fonts.system,
    backgroundColor: BrutalistTheme.colors.white,
    color: BrutalistTheme.colors.grey800,
  },
  resetButton: {
    backgroundColor: BrutalistTheme.colors.black,
    paddingVertical: BrutalistTheme.spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: BrutalistTheme.spacing.lg,
    borderWidth: 1,
    borderColor: BrutalistTheme.colors.black,
    borderRadius: BrutalistTheme.borderRadius.sm,
  },
  resetButtonDisabled: {
    backgroundColor: BrutalistTheme.colors.grey400,
    borderColor: BrutalistTheme.colors.grey400,
  },

  // Success state
  successContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: BrutalistTheme.spacing.md,
  },
  successMessage: {
    textAlign: 'center',
    marginBottom: BrutalistTheme.spacing.sm,
  },
  emailText: {
    textAlign: 'center',
    marginBottom: BrutalistTheme.spacing.lg,
  },
  instructionText: {
    textAlign: 'center',
    lineHeight: 20,
    maxWidth: 280,
  },

  // Footer
  footer: {
    alignItems: 'center',
    paddingTop: BrutalistTheme.spacing.xl,
  },
  backButton: {
    paddingVertical: BrutalistTheme.spacing.md,
    alignItems: 'center',
  },
});