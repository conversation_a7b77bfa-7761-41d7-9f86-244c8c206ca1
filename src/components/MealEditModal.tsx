import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { BrutalistText } from './BrutalistText';
import { AddFoodButton } from './AddFoodButton';
import { FoodSearchModal } from './FoodSearchModal';
import { BrutalistTheme } from '../theme/colors';
import { LoggedFood, MealSession } from '../types/food';

interface MealEditModalProps {
  visible: boolean;
  session: MealSession | null;
  onSave: (editedFood: LoggedFood) => void;
  onAddFoodToSession: (sessionId: string, food: LoggedFood) => void;
  onCancel: () => void;
}

export const MealEditModal: React.FC<MealEditModalProps> = ({
  visible,
  session,
  onSave,
  onCancel,
  onAddFoodToSession,
}) => {
  const [editedFoods, setEditedFoods] = useState<LoggedFood[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [showFoodSearch, setShowFoodSearch] = useState(false);

  // Update local state when session changes - make deep copy
  useEffect(() => {
    if (session) {
      console.log(
        'Initializing editedFoods with session foods:',
        session.foods.map(f => f.name),
      );
      setEditedFoods(session.foods.map(food => ({ ...food })));
    }
  }, [session]);

  const toggleFoodItem = (foodIndex: number) => {
    const newFoods = [...editedFoods];
    const food = newFoods[foodIndex];
    const originalQuantity =
      session?.foods.find(f => f.id === food.id)?.quantity || 1;
    // Toggle between 0 and original quantity
    food.quantity = food.quantity === 0 ? originalQuantity : 0;
    setEditedFoods(newFoods);
  };

  const updateQuantity = (foodIndex: number, newQuantity: string) => {
    // Allow empty string during editing
    if (newQuantity === '') {
      const newFoods = [...editedFoods];
      newFoods[foodIndex].quantity = 0;
      setEditedFoods(newFoods);
      return;
    }

    const quantity = parseFloat(newQuantity);
    if (!isNaN(quantity) && quantity >= 0) {
      const newFoods = [...editedFoods];
      newFoods[foodIndex].quantity = quantity;
      setEditedFoods(newFoods);
    }
  };

  const handleSaveAll = async () => {
    if (!session || isSaving) return;

    console.log('handleSaveAll called!');
    console.log(
      'Session foods:',
      session.foods.map(f => ({ name: f.name, quantity: f.quantity })),
    );
    console.log(
      'Edited foods:',
      editedFoods.map(f => ({ name: f.name, quantity: f.quantity })),
    );

    setIsSaving(true);

    try {
      // Save each modified food
      for (const food of editedFoods) {
        const originalFood = session.foods.find(f => f.id === food.id);
        console.log(
          `Checking food: ${food.name}, original: ${originalFood?.quantity}, edited: ${food.quantity}`,
        );

        if (!originalFood) {
          // This is a new food, use the dedicated add-to-session handler
          console.log('Adding new food to session:', food.name);
          await onAddFoodToSession(session.id, food);
        } else if (food.quantity !== originalFood.quantity) {
          // Existing food with quantity change - save
          console.log(
            'Saving modified food:',
            food.name,
            'quantity changed from',
            originalFood.quantity,
            'to',
            food.quantity,
          );
          await onSave(food);
        } else {
          // Existing food with no changes - skip
          console.log('No changes detected for:', food.name);
        }
      }

      // Close modal after all saves are complete
      onCancel();
    } catch (error) {
      console.error('Error saving meal changes:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const getTotalCalories = () => {
    return editedFoods.reduce(
      (sum, food) => sum + food.calories * food.quantity,
      0,
    );
  };

  const handleAddFood = (food: LoggedFood) => {
    if (isSaving) return;

    try {
      console.log(
        'handleAddFood called with:',
        food.name,
        'quantity:',
        food.quantity,
      );
      console.log(
        'Current editedFoods before adding:',
        editedFoods.map(f => f.name),
      );

      // Add the new food to the edited foods list (no immediate save)
      setEditedFoods(prev => {
        const newFoods = [...prev, food];
        console.log(
          'New editedFoods after adding:',
          newFoods.map(f => f.name),
        );
        return newFoods;
      });

      // Close the search modal
      setShowFoodSearch(false);
    } catch (error) {
      console.error('Error adding food from search:', error);
      throw error; // Re-throw to let modal handle error display
    }
  };

  if (!session) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onCancel}
    >
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.container}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity
              style={[styles.closeButton, isSaving && styles.disabledButton]}
              onPress={isSaving ? undefined : onCancel}
              activeOpacity={isSaving ? 1 : 0.7}
            >
              <BrutalistText
                variant="callout"
                weight="bold"
                fontFamily="mono"
                color={isSaving ? 'grey400' : 'grey700'}
              >
                ✕
              </BrutalistText>
            </TouchableOpacity>

            <View style={styles.headerContent}>
              <BrutalistText
                variant="title3"
                weight="black"
                fontFamily="system"
                color="grey800"
                style={styles.headerTitle}
              >
                EDIT
              </BrutalistText>
              <BrutalistText
                variant="caption1"
                weight="medium"
                fontFamily="mono"
                color="grey600"
              >
                {session.meal_type.toUpperCase()}
              </BrutalistText>
            </View>

            <View style={styles.headerSpacer} />
          </View>

          {/* Updated Totals */}
          <View style={styles.summarySection}>
            <BrutalistText
              variant="caption1"
              weight="medium"
              fontFamily="mono"
              color="grey700"
              style={styles.sectionLabel}
            >
              UPDATED TOTALS
            </BrutalistText>

            <View style={styles.summaryRow}>
              <View style={styles.summaryItem}>
                <BrutalistText
                  variant="title3"
                  weight="bold"
                  fontFamily="mono"
                  color="accent1"
                >
                  {Math.round(getTotalCalories())}
                </BrutalistText>
                <BrutalistText
                  variant="caption2"
                  weight="medium"
                  color="grey600"
                >
                  CALORIES
                </BrutalistText>
              </View>

              <View style={styles.summaryItem}>
                <BrutalistText
                  variant="callout"
                  weight="semibold"
                  fontFamily="mono"
                  color="grey800"
                >
                  {editedFoods.filter(f => f.quantity > 0).length}
                </BrutalistText>
                <BrutalistText
                  variant="caption2"
                  weight="medium"
                  color="grey600"
                >
                  ITEMS
                </BrutalistText>
              </View>
            </View>
          </View>

          {/* Food Items List - Streamlined */}
          <ScrollView
            style={styles.content}
            showsVerticalScrollIndicator={false}
          >
            <View style={styles.foodsList}>
              <BrutalistText
                variant="caption1"
                weight="medium"
                fontFamily="mono"
                color="grey700"
                style={styles.sectionLabel}
              >
                ADJUST QUANTITIES
              </BrutalistText>

              {editedFoods.map((food, index) => {
                console.log(`Rendering food item ${index}: ${food.name}`);
                return (
                  <View key={`${food.id}-${index}`} style={styles.foodItemRow}>
                    <View style={styles.foodToggle}>
                      <TouchableOpacity
                        style={[
                          styles.foodNameArea,
                          food.quantity === 0 && styles.foodToggleDisabled,
                        ]}
                        onPress={() => toggleFoodItem(index)}
                        activeOpacity={0.7}
                      >
                        <View style={styles.foodInfo}>
                          <BrutalistText
                            variant="callout"
                            weight="medium"
                            color={food.quantity === 0 ? 'grey500' : 'grey800'}
                            style={
                              food.quantity === 0
                                ? styles.strikethrough
                                : undefined
                            }
                          >
                            {food.name}
                          </BrutalistText>
                          <BrutalistText
                            variant="caption2"
                            weight="medium"
                            color={food.quantity === 0 ? 'grey400' : 'grey600'}
                            fontFamily="mono"
                          >
                            {Math.round(food.calories)} CAL EACH
                          </BrutalistText>
                        </View>
                      </TouchableOpacity>

                      <View style={styles.quantityControls}>
                        <TextInput
                          style={[
                            styles.quantityInput,
                            food.quantity === 0 && styles.quantityInputDisabled,
                          ]}
                          value={
                            food.quantity === 0 ? '' : food.quantity.toString()
                          }
                          onChangeText={text => updateQuantity(index, text)}
                          keyboardType="numeric"
                          placeholder="0"
                          placeholderTextColor={BrutalistTheme.colors.grey500}
                        />
                        <BrutalistText
                          variant="caption2"
                          weight="medium"
                          color={food.quantity === 0 ? 'grey400' : 'grey700'}
                        >
                          ×
                        </BrutalistText>
                      </View>
                    </View>
                  </View>
                );
              })}

              {/* Add Food Button */}
              <View style={styles.addFoodContainer}>
                <AddFoodButton
                  onPress={() => setShowFoodSearch(true)}
                  disabled={isSaving}
                />
              </View>
            </View>
          </ScrollView>

          {/* Action Buttons - Exactly like FoodResults */}
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.cancelAction}
              onPress={onCancel}
              activeOpacity={0.7}
              disabled={isSaving}
            >
              <BrutalistText
                variant="body"
                weight="medium"
                color={isSaving ? 'grey400' : 'grey700'}
                fontFamily="system"
              >
                Cancel
              </BrutalistText>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.saveAction, isSaving && styles.disabledAction]}
              onPress={handleSaveAll}
              activeOpacity={0.7}
              disabled={isSaving}
            >
              <View style={styles.saveButtonContent}>
                {isSaving && (
                  <ActivityIndicator
                    size="small"
                    color={BrutalistTheme.colors.grey600}
                    style={styles.loadingIndicator}
                  />
                )}
                <BrutalistText
                  variant="body"
                  weight="medium"
                  color={isSaving ? 'grey500' : 'black'}
                  fontFamily="system"
                >
                  {isSaving ? 'Saving...' : 'Save Changes'}
                </BrutalistText>
              </View>
              <View
                style={[
                  styles.saveActionUnderline,
                  isSaving && styles.disabledUnderline,
                ]}
              />
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>

      {/* Food Search Modal */}
      <FoodSearchModal
        visible={showFoodSearch}
        mealType={session?.meal_type || 'snack'}
        onAddFood={handleAddFood}
        onCancel={() => setShowFoodSearch(false)}
      />
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BrutalistTheme.colors.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingTop: BrutalistTheme.spacing.lg,
    paddingBottom: BrutalistTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey200,
  },
  closeButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    opacity: 0.5,
  },
  headerContent: {
    alignItems: 'center',
    gap: 4,
  },
  headerTitle: {
    letterSpacing: 1,
  },
  headerSpacer: {
    width: 44,
  },
  summarySection: {
    backgroundColor: BrutalistTheme.colors.white,
    paddingTop: 0,
    paddingBottom: BrutalistTheme.spacing.sm,
    paddingHorizontal: BrutalistTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey200,
    alignItems: 'center',
    gap: BrutalistTheme.spacing.sm,
  },
  sectionLabel: {
    textAlign: 'center',
    letterSpacing: 1,
    marginBottom: BrutalistTheme.spacing.xs,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  summaryItem: {
    alignItems: 'center',
    flex: 1,
    gap: 2,
  },
  content: {
    flex: 1,
  },
  foodsList: {
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingTop: BrutalistTheme.spacing.sm,
    paddingBottom: BrutalistTheme.spacing.lg,
    gap: 0,
  },
  foodItemRow: {
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey200,
  },
  foodToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: BrutalistTheme.spacing.sm,
    paddingHorizontal: BrutalistTheme.spacing.md,
    backgroundColor: BrutalistTheme.colors.white,
  },
  foodNameArea: {
    flex: 1,
    paddingRight: BrutalistTheme.spacing.md,
  },
  foodToggleDisabled: {
    backgroundColor: BrutalistTheme.colors.surface,
    opacity: 0.6,
  },
  foodInfo: {
    flex: 1,
    gap: 4,
  },
  strikethrough: {
    textDecorationLine: 'line-through',
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BrutalistTheme.spacing.xs,
  },
  quantityInput: {
    borderWidth: 2,
    borderColor: BrutalistTheme.colors.grey300,
    borderRadius: BrutalistTheme.borderRadius.sm,
    paddingHorizontal: BrutalistTheme.spacing.sm,
    paddingVertical: BrutalistTheme.spacing.xs,
    fontSize: 16,
    fontFamily: BrutalistTheme.fonts.mono,
    color: BrutalistTheme.colors.grey800,
    backgroundColor: BrutalistTheme.colors.white,
    textAlign: 'center',
    width: 60,
  },
  quantityInputDisabled: {
    backgroundColor: BrutalistTheme.colors.surface,
    borderColor: BrutalistTheme.colors.grey200,
    color: BrutalistTheme.colors.grey500,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: BrutalistTheme.spacing.lg,
    borderTopWidth: 1,
    borderTopColor: BrutalistTheme.colors.grey200,
  },
  cancelAction: {
    paddingVertical: BrutalistTheme.spacing.xs,
    paddingHorizontal: BrutalistTheme.spacing.xs,
  },
  saveAction: {
    alignItems: 'flex-end',
    paddingVertical: BrutalistTheme.spacing.xs,
  },
  saveButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BrutalistTheme.spacing.xs,
  },
  loadingIndicator: {
    marginRight: 2,
  },
  saveActionUnderline: {
    height: 2,
    backgroundColor: BrutalistTheme.colors.grey700,
    marginTop: 2,
    width: '100%',
  },
  disabledAction: {
    opacity: 0.5,
  },
  disabledUnderline: {
    backgroundColor: BrutalistTheme.colors.grey400,
  },
  addFoodContainer: {
    alignItems: 'center',
    paddingTop: BrutalistTheme.spacing.md,
    paddingBottom: BrutalistTheme.spacing.sm,
  },
});
