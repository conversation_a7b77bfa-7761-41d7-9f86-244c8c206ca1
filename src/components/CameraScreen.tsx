import React, { useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  ActivityIndicator,
  TouchableOpacity,
  Platform,
} from 'react-native';
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';
import { Brutal<PERSON>Button, BrutalistText, BrutalistView } from './index';
import { AnalyzingScreen } from './AnalyzingScreen';
import { BrutalistTheme } from '../theme/colors';
import { aiService, RecognitionResult } from '../services/aiService';
import { lidarMeasurementService } from '../services/lidarMeasurementService';
import { ARPreviewView } from '../native/ARPreviewView';

interface CameraScreenProps {
  onFoodRecognized: (result: RecognitionResult) => void;
  onManualAdd?: () => void;
}

export const CameraScreen: React.FC<CameraScreenProps> = ({
  onFoodRecognized,
  onManualAdd,
}) => {
  const [cameraAuthorized, setCameraAuthorized] = useState<boolean | null>(
    null,
  );
  const [isProcessing, setIsProcessing] = useState(false);

  React.useEffect(() => {
    console.log('📱 [CameraScreen] Component mounted');
    checkCameraPermission();
  }, []);

  const checkCameraPermission = async () => {
    if (Platform.OS !== 'ios') {
      setCameraAuthorized(true);
      return;
    }
    try {
      const status = await check(PERMISSIONS.IOS.CAMERA);
      if (status === RESULTS.GRANTED) {
        setCameraAuthorized(true);
      } else {
        const req = await request(PERMISSIONS.IOS.CAMERA);
        setCameraAuthorized(req === RESULTS.GRANTED);
      }
    } catch (e) {
      console.warn('Camera permission check failed', e);
      setCameraAuthorized(false);
    }
  };

  const takePhoto = useCallback(async () => {
    console.log('📸 [CameraScreen] Starting photo capture');
    setIsProcessing(true);

    try {
      // iOS: Capture a still image with ARKit + LiDAR depth
      const { imagePath } =
        await lidarMeasurementService.capturePhotoWithDepth();
      console.log('✅ [CameraScreen] Photo captured with depth');

      const imageUri = imagePath;

      // Step 3: Analyze with AI (including measurement data if available)
      console.log('🤖 [CameraScreen] Starting AI analysis...');
      let result = await aiService.recognizeFood(imageUri);

      console.log('✅ [CameraScreen] AI analysis complete');
      onFoodRecognized(result);
    } catch (error) {
      console.error('❌ [CameraScreen] Photo processing error:', error);

      // Show specific error messages based on the error type
      let errorMessage = 'Failed to analyze food. Please try again.';
      if (error instanceof Error) {
        if (error.message.includes('API key')) {
          errorMessage = 'API key not configured. Please check your setup.';
        } else if (
          error.message.includes('Rate limit') ||
          error.message.includes('quota')
        ) {
          errorMessage =
            'Please add credits to your OpenAI account to continue.';
        } else if (error.message.includes('clearer image')) {
          errorMessage =
            'Could not analyze the image. Please try a clearer photo with better lighting.';
        }
      }

      // iOS fix: delay alert to avoid modal/spinner conflicts
      setTimeout(() => {
        Alert.alert('Food Analysis Failed', errorMessage, [
          { text: 'Try Again', style: 'default' },
        ]);
      }, 500);
    } finally {
      setIsProcessing(false);
      console.log('🏁 [CameraScreen] Photo processing complete');
    }
  }, [onFoodRecognized]);

  if (cameraAuthorized === false) {
    return (
      <BrutalistView style={styles.permissionContainer}>
        <BrutalistText variant="title2" style={styles.permissionText}>
          Camera Permission Required
        </BrutalistText>
        <BrutalistText style={styles.permissionDescription}>
          Please allow camera access to scan food items
        </BrutalistText>
        <BrutalistButton
          title="Request Permission"
          onPress={checkCameraPermission}
          style={styles.permissionButton}
        />
      </BrutalistView>
    );
  }

  if (cameraAuthorized === null) {
    return (
      <BrutalistView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={BrutalistTheme.colors.primary} />
        <BrutalistText style={styles.loadingText}>Preparing...</BrutalistText>
      </BrutalistView>
    );
  }

  // Show analyzing screen when processing
  if (isProcessing) {
    return <AnalyzingScreen />;
  }

  return (
    <View style={styles.container}>
      {/* Live ARKit preview */}
      <ARPreviewView style={styles.camera} />
      <View style={styles.overlay}>
        <View style={styles.header}>
          <BrutalistText
            variant="caption1"
            weight="black"
            color="white"
            fontFamily="system"
            style={styles.appName}
          >
            KOA
          </BrutalistText>
        </View>

        <View style={styles.scanArea} />

        <View style={styles.controls}>
          <View style={styles.unifiedButtonContainer}>
            {/* Main Scan Button */}
            <TouchableOpacity
              onPress={takePhoto}
              style={styles.mainScanButton}
              activeOpacity={0.7}
            >
              <View style={styles.scanButtonInner} />
            </TouchableOpacity>

            {/* Manual Add Extension */}
            {onManualAdd && (
              <TouchableOpacity
                onPress={onManualAdd}
                style={styles.manualAddExtension}
                activeOpacity={0.7}
              >
                <BrutalistText
                  variant="caption1"
                  weight="bold"
                  color="white"
                  fontFamily="mono"
                  style={styles.manualAddIcon}
                >
                  +
                </BrutalistText>
              </TouchableOpacity>
            )}
          </View>

          {/* Subtle Labels */}
          {onManualAdd && (
            <View style={styles.buttonLabels}>
              <BrutalistText
                variant="caption2"
                weight="medium"
                color="white"
                fontFamily="mono"
                style={styles.scanLabel}
              >
                SCAN
              </BrutalistText>
              <BrutalistText
                variant="caption2"
                weight="medium"
                color="white"
                fontFamily="mono"
                style={styles.addLabel}
              >
                ADD
              </BrutalistText>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BrutalistTheme.colors.black,
  },
  camera: {
    flex: 1,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
    paddingVertical: BrutalistTheme.spacing.xl,
    paddingHorizontal: BrutalistTheme.spacing.md,
  },
  header: {
    alignItems: 'center',
    paddingTop: BrutalistTheme.spacing.sm,
  },
  appName: {
    letterSpacing: 0,
    opacity: 0.8,
  },
  scanArea: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  controls: {
    alignItems: 'center',
    paddingBottom: BrutalistTheme.spacing.md,
    gap: BrutalistTheme.spacing.sm,
  },
  unifiedButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: BrutalistTheme.borderWidth.thin,
    borderColor: BrutalistTheme.colors.white,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 50,
    overflow: 'hidden',
  },
  mainScanButton: {
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: BrutalistTheme.colors.white,
    opacity: 0.9,
  },
  manualAddExtension: {
    width: 60,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    borderLeftWidth: BrutalistTheme.borderWidth.thin,
    borderLeftColor: BrutalistTheme.colors.white,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  manualAddIcon: {
    fontSize: 20,
    lineHeight: 20,
    opacity: 0.9,
  },
  buttonLabels: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BrutalistTheme.spacing.xl,
    opacity: 0.6,
  },
  scanLabel: {
    letterSpacing: 1,
    marginLeft: BrutalistTheme.spacing.md,
  },
  addLabel: {
    letterSpacing: 1,
    marginRight: BrutalistTheme.spacing.sm,
  },

  // Permission styles
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: BrutalistTheme.colors.white,
  },
  permissionText: {
    textAlign: 'center',
    marginBottom: BrutalistTheme.spacing.md,
  },
  permissionDescription: {
    textAlign: 'center',
    marginBottom: BrutalistTheme.spacing.xl,
    paddingHorizontal: BrutalistTheme.spacing.lg,
  },
  permissionButton: {
    width: '80%',
  },

  // Loading styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: BrutalistTheme.colors.white,
  },
  loadingText: {
    marginTop: BrutalistTheme.spacing.md,
  },
});
