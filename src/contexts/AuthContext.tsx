import React, { createContext, useContext, useEffect, useState } from 'react'
import { Platform, AppState } from 'react-native'
import { User } from '@supabase/supabase-js'
import { authService } from '../services/authService'
import { syncService } from '../services/syncService'
import { supabase } from '../services/supabase'
import { foodLogService } from '../services/foodLogService'

// Timeout wrapper for auth operations to prevent stuck loading
const withAuthTimeout = async <T,>(
  operation: () => Promise<T>,
  timeoutMs: number = 5000,
  operationName: string = 'auth operation'
): Promise<T> => {
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => reject(new Error(`${operationName} timed out after ${timeoutMs}ms`)), timeoutMs);
  });
  
  return Promise.race([operation(), timeoutPromise]);
};

interface Profile {
  id: string
  username?: string
  full_name?: string
  avatar_url?: string
  daily_calorie_goal: number
  daily_protein_goal: number
  daily_carb_goal: number
  daily_fat_goal: number
  created_at: string
  updated_at: string
}

interface AuthContextType {
  user: User | null
  profile: Profile | null
  loading: boolean
  isPasswordRecovery: boolean
  signUp: (email: string, password: string) => Promise<{ data: any; error: any }>
  signIn: (email: string, password: string) => Promise<{ data: any; error: any }>
  signInWithApple: () => Promise<{ data: any; error: any }>
  signInWithGoogle: () => Promise<{ data: any; error: any }>
  signInWithDiscord: () => Promise<{ data: any; error: any }>
  signOut: () => Promise<{ error: any }>
  createProfile: (data: { username?: string; full_name?: string }) => Promise<{ data: any; error: any }>
  updateProfile: (updates: {
    username?: string;
    full_name?: string;
    daily_calorie_goal?: number;
    daily_protein_goal?: number;
    daily_carb_goal?: number;
    daily_fat_goal?: number;
  }) => Promise<{ data: any; error: any }>
  resetPasswordForEmail: (email: string) => Promise<{ data: any; error: any }>
  updatePassword: (password: string) => Promise<{ data: any; error: any }>
  completePasswordRecovery: () => void
  setPasswordRecoveryMode: (isRecovery: boolean) => void
  setRecoveryTokens: (tokens: {access_token: string, refresh_token: string}) => void
  isAppleSignInAvailable: boolean
}

const AuthContext = createContext<AuthContextType>({} as AuthContextType)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)
  const [isPasswordRecovery, setIsPasswordRecovery] = useState(false)
  const [recoveryTokens, setRecoveryTokens] = useState<{access_token: string, refresh_token: string} | null>(null)
  const [isAppleSignInAvailable, setIsAppleSignInAvailable] = useState(false)

  useEffect(() => {
    // Loading state safety net - prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      console.warn('Loading timeout reached - forcing loading to false after 15s')
      setLoading(false)
    }, 15000)

    // Get initial session and check Apple Sign-In availability
    const getInitialSession = async () => {
      try {
        // Check Apple Sign-In availability (temporarily force to true on iOS for testing)
        const appleAvailable = Platform.OS === 'ios' // await authService.isAppleSignInAvailable()
        setIsAppleSignInAvailable(appleAvailable)
        
        const { data: { user } } = await withAuthTimeout(
          () => authService.getCurrentUser(),
          5000,
          'initial session check'
        )
        console.log('Initial session check - user:', user ? 'signed in' : 'not signed in')
        setUser(user)
        
        if (user) {
          // Get user profile
          const { data: profile } = await withAuthTimeout(
            () => authService.getProfile(user.id),
            5000,
            'profile loading'
          )
          setProfile(profile)
          
          // Initialize sync service for local-first functionality (non-blocking)
          withAuthTimeout(
            () => syncService.initialize(user.id),
            10000,
            'sync service initialization'
          ).catch(error => {
            console.error('Error initializing sync service:', error)
            // Don't block auth flow - sync will retry later
          })
        }
      } catch (error) {
        console.error('Error getting initial session:', error)
      } finally {
        clearTimeout(loadingTimeout)
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = authService.onAuthStateChange(
      async (event, session) => {
        try {
          console.log('Auth state change event:', event, 'isPasswordRecovery:', isPasswordRecovery)
          
          // Handle password recovery event FIRST - before setting user
          if (event === 'PASSWORD_RECOVERY') {
            console.log('PASSWORD_RECOVERY event detected - enabling recovery mode but keeping session for password update')
            setIsPasswordRecovery(true)
            // Keep the session for password updates but don't show as logged in
            setUser(null) // Don't show as logged in UI-wise
            setProfile(null)
            setLoading(false)
            return // Exit early - don't process the session as a normal login
          }

          // If we're in password recovery mode OR have recovery tokens, don't process SIGNED_IN events
          if ((isPasswordRecovery || recoveryTokens) && event === 'SIGNED_IN') {
            console.log('Ignoring SIGNED_IN event during password recovery mode - maintaining recovery state')
            setLoading(false)
            return
          }

          // Only process normal login if NOT in password recovery
          if (!isPasswordRecovery) {
            setUser(session?.user ?? null)
            
            if (session?.user) {
              // Get user profile when signed in
              const { data: profile } = await withAuthTimeout(
                () => authService.getProfile(session.user.id),
                5000,
                'auth state profile loading'
              )
              setProfile(profile)
            } else {
              setProfile(null)
            }
          }
        } catch (error) {
          console.error('Error in auth state change:', error)
        } finally {
          setLoading(false)
        }
      }
    )

    // Coordinated app state handler - manages both auth refresh and sync operations
    const handleAppStateChange = async (nextAppState: string) => {
      console.log('App state changed to:', nextAppState)
      
      if (nextAppState === 'active') {
        console.log('App became active - coordinating auth refresh and sync cleanup')
        
        try {
          // Step 1: Start auto-refresh for auth tokens (Supabase best practice)
          supabase.auth.startAutoRefresh()
          
          // Step 2: Check current session with timeout
          const { data: { user } } = await withAuthTimeout(
            () => authService.getCurrentUser(),
            5000,
            'app resume session check'
          )
          console.log('Session check result - User:', user ? 'signed in' : 'not signed in')
          
          // Step 3: Handle user state and cleanup
          if (user) {
            // Clean up any stuck sync operations when we have a valid user
            foodLogService.cleanupOnAppResume()
          } else {
            // No user found - session may have been lost
            console.log('No user found on app resume - session may have expired')
            setUser(null)
            setProfile(null)
          }
        } catch (error) {
          console.error('Error during coordinated app resume:', error)
          setLoading(false)
        }
      } else {
        // App going to background/inactive - stop auto-refresh
        console.log('App going to background - stopping auth auto-refresh')
        supabase.auth.stopAutoRefresh()
      }
    }

    const appStateSubscription = AppState.addEventListener('change', handleAppStateChange)

    return () => {
      clearTimeout(loadingTimeout)
      subscription.unsubscribe()
      appStateSubscription?.remove()
    }
  }, [])

  const signUp = async (email: string, password: string) => {
    setLoading(true)
    const result = await authService.signUp(email, password)
    setLoading(false)
    return result
  }

  const signIn = async (email: string, password: string) => {
    setLoading(true)
    const result = await authService.signIn(email, password)
    setLoading(false)
    return result
  }

  const signInWithApple = async () => {
    setLoading(true)
    const result = await authService.signInWithApple()
    setLoading(false)
    return result
  }

  const signInWithGoogle = async () => {
    setLoading(true)
    const result = await authService.signInWithGoogle()
    setLoading(false)
    return result
  }

  const signInWithDiscord = async () => {
    setLoading(true)
    const result = await authService.signInWithDiscord()
    setLoading(false)
    return result
  }

  const signOut = async () => {
    setLoading(true)
    
    try {
      const result = await authService.signOut()
      
      // Only cleanup sync service if sign out was successful
      if (!result.error) {
        try {
          syncService.cleanup()
        } catch (cleanupError) {
          console.error('Error during sync service cleanup:', cleanupError)
        }
      }
      
      setLoading(false)
      return result
    } catch (error) {
      console.error('Error during sign out:', error)
      setLoading(false)
      return { error }
    }
  }

  const createProfile = async (data: { username?: string; full_name?: string }) => {
    if (!user) return { data: null, error: { message: 'No user found' } }
    
    const result = await authService.createProfile(user.id, data)
    if (result.data) {
      setProfile(result.data)
    }
    return result
  }

  const updateProfile = async (updates: {
    username?: string;
    full_name?: string;
    daily_calorie_goal?: number;
    daily_protein_goal?: number;
    daily_carb_goal?: number;
    daily_fat_goal?: number;
  }) => {
    if (!user) return { data: null, error: { message: 'No user found' } }
    
    const result = await authService.updateProfile(user.id, updates)
    if (result.data) {
      setProfile(result.data)
    }
    return result
  }

  const resetPasswordForEmail = async (email: string) => {
    return await authService.resetPasswordForEmail(email)
  }

  const updatePassword = async (password: string) => {
    // If we have recovery tokens, use them directly for password update
    if (recoveryTokens) {
      try {
        console.log('Using recovery tokens for direct password update')
        // Use recovery tokens to update password directly
        const result = await authService.updatePasswordWithTokens(password, recoveryTokens.access_token, recoveryTokens.refresh_token)
        
        // Clear recovery tokens after use
        setRecoveryTokens(null)
        return result
      } catch (err) {
        console.error('Recovery password update failed:', err)
        return { data: null, error: err }
      }
    }
    
    return await authService.updatePassword(password)
  }

  const completePasswordRecovery = async () => {
    setIsPasswordRecovery(false)
    // After completing password recovery, get the current session
    try {
      const { data: { user } } = await withAuthTimeout(
        () => authService.getCurrentUser(),
        5000,
        'password recovery session check'
      )
      setUser(user)
      
      if (user) {
        const { data: profile } = await withAuthTimeout(
          () => authService.getProfile(user.id),
          5000,
          'password recovery profile loading'
        )
        setProfile(profile)
      }
    } catch (error) {
      console.error('Error completing password recovery:', error)
    }
  }

  const setPasswordRecoveryMode = (isRecovery: boolean) => {
    console.log('Setting password recovery mode:', isRecovery)
    setIsPasswordRecovery(isRecovery)
    if (isRecovery) {
      // Clear user when entering recovery mode
      setUser(null)
      setProfile(null)
    }
  }

  const setRecoveryTokensHandler = (tokens: {access_token: string, refresh_token: string}) => {
    console.log('Setting recovery tokens for password update')
    setRecoveryTokens(tokens)
  }

  const value = {
    user,
    profile,
    loading,
    isPasswordRecovery,
    signUp,
    signIn,
    signInWithApple,
    signInWithGoogle,
    signInWithDiscord,
    signOut,
    createProfile,
    updateProfile,
    resetPasswordForEmail,
    updatePassword,
    completePasswordRecovery,
    setPasswordRecoveryMode,
    setRecoveryTokens: setRecoveryTokensHandler,
    isAppleSignInAvailable,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}