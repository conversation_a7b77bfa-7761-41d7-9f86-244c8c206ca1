/**
 * Extracts quantity from portion estimate strings
 * Examples:
 * "3 medium bananas" -> 3
 * "2 slices of bread" -> 2
 * "1 large apple" -> 1
 * "half an avocado" -> 0.5
 * "one cup of rice" -> 1
 */
export const extractQuantityFromPortionEstimate = (portionEstimate: string): number => {
  if (!portionEstimate) return 1;
  
  const portion = portionEstimate.toLowerCase().trim();
  
  // Handle fractions and words
  const fractionMap: { [key: string]: number } = {
    'half': 0.5,
    '1/2': 0.5,
    'quarter': 0.25,
    '1/4': 0.25,
    'third': 0.33,
    '1/3': 0.33,
    'two-thirds': 0.67,
    '2/3': 0.67,
  };
  
  // Handle number words
  const numberWords: { [key: string]: number } = {
    'zero': 0,
    'one': 1,
    'two': 2,
    'three': 3,
    'four': 4,
    'five': 5,
    'six': 6,
    'seven': 7,
    'eight': 8,
    'nine': 9,
    'ten': 10,
    'eleven': 11,
    'twelve': 12,
  };
  
  // Check for fractions first
  for (const [word, value] of Object.entries(fractionMap)) {
    if (portion.includes(word)) {
      return value;
    }
  }
  
  // Look for number at the beginning of the string
  const numberMatch = portion.match(/^(\d+(?:\.\d+)?)/);
  if (numberMatch) {
    return parseFloat(numberMatch[1]);
  }
  
  // Look for decimal numbers
  const decimalMatch = portion.match(/(\d+\.\d+)/);
  if (decimalMatch) {
    return parseFloat(decimalMatch[1]);
  }
  
  // Check for number words
  for (const [word, value] of Object.entries(numberWords)) {
    if (portion.startsWith(word + ' ') || portion === word) {
      return value;
    }
  }
  
  // Look for "a" or "an" which typically means 1
  if (portion.match(/^(a |an )/)) {
    return 1;
  }
  
  // Default to 1 if no quantity detected
  return 1;
};

/**
 * Generates a user-friendly description of the detected quantity
 */
export const getQuantityDescription = (quantity: number): string => {
  if (quantity === 1) return '';
  if (quantity === 0.5) return '(½)';
  if (quantity === 0.25) return '(¼)';
  if (quantity === 0.33) return '(⅓)';
  if (quantity === 0.67) return '(⅔)';
  if (quantity % 1 === 0) return `(${quantity}x)`;
  return `(${quantity.toFixed(1)}x)`;
};