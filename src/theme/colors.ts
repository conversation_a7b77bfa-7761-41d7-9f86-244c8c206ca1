export const BrutalistColors = {
  // Core brutalist colors
  black: '#000000',
  white: '#FFFFFF',
  
  // Apple-compliant brutalist colors (neo-brutalist approach)
  primary: '#FF3B30',      // iOS system red with brutalist edge
  secondary: '#007AFF',    // iOS system blue 
  warning: '#FF9500',      // iOS system orange (more accessible than yellow)
  success: '#34C759',      // iOS system green
  error: '#FF3B30',        // iOS system red for deletions
  
  // iOS semantic greys
  grey100: '#F2F2F7',     // iOS systemGray6
  grey200: '#E5E5EA',     // iOS systemGray5  
  grey300: '#D1D1D6',     // iOS systemGray4
  grey400: '#C7C7CC',     // iOS systemGray3
  grey500: '#AEAEB2',     // iOS systemGray2
  grey600: '#8E8E93',     // iOS systemGray
  grey700: '#48484A',     // iOS systemGray
  grey800: '#2C2C2E',     // iOS systemGray
  grey900: '#1C1C1E',     // iOS label secondary
  
  // Additional brutalist accent colors
  accent1: '#5856D6',     // iOS system purple
  accent2: '#AF52DE',     // iOS system purple variant
  accent3: '#4A148C',     // Deep purple for titles
  
  // Surface colors for iOS
  background: '#FFFFFF',
  surface: '#F2F2F7',
  elevated: '#FFFFFF',
};

export const BrutalistTheme = {
  colors: BrutalistColors,
  
  // Font families - Mixed serif/sans-serif system
  fonts: {
    // Sans-serif for UI elements, brutalist headers
    system: 'System',
    systemBold: 'System',
    
    // Serif for body text, comfortable reading
    serif: 'Georgia', // Fallback to Georgia (available on both platforms)
    serifSystem: 'Times New Roman', // iOS/Android fallback
    
    // iOS 13+ system serif (when available)
    systemSerif: 'System-Serif',
    
    // Monospace for code/data
    mono: 'Courier New',
  },
  
  // iOS-compliant typography scale
  fontSizes: {
    caption2: 11,     // iOS caption 2
    caption1: 12,     // iOS caption 1  
    footnote: 13,     // iOS footnote
    subheadline: 15,  // iOS subheadline
    callout: 16,      // iOS callout
    body: 17,         // iOS body (default)
    headline: 17,     // iOS headline
    title3: 20,       // iOS title 3
    title2: 22,       // iOS title 2
    title1: 28,       // iOS title 1
    largeTitle: 34,   // iOS large title
    
    // Brutalist scale (larger, bolder versions)
    brutalistBody: 18,
    brutalistHeadline: 24,
    brutalistTitle: 32,
    brutalistHero: 48,
  },
  
  // iOS-style spacing (8pt grid system)
  spacing: {
    xxs: 2,
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
    xxxl: 64,
  },
  
  // Refined border widths for neo-brutalist style
  borderWidth: {
    hairline: 0.5,    // iOS hairline
    thin: 1,
    medium: 2,
    thick: 3,         // Reduced from 4 for better iOS compliance
    brutal: 5,        // Reduced from 8 for better iOS compliance
  },
  
  // iOS-compliant corner radius
  borderRadius: {
    none: 0,
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 20,
    full: 9999,
  },
  
  // Refined shadows - less harsh than pure brutalist
  shadows: {
    subtle: {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    brutalist: {
      shadowColor: '#000000',
      shadowOffset: { width: 2, height: 2 },
      shadowOpacity: 0.8,
      shadowRadius: 0,
      elevation: 4,
    },
    strong: {
      shadowColor: '#000000',
      shadowOffset: { width: 3, height: 3 },
      shadowOpacity: 1,
      shadowRadius: 0,
      elevation: 6,
    },
  },
};