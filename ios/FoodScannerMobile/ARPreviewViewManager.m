#import <React/RCTViewManager.h>
#import <ARKit/ARKit.h>

@interface ARPreviewViewManager : RCTViewManager
@end

@implementation ARPreviewViewManager

RCT_EXPORT_MODULE(ARPreviewView)

+ (BOOL)requiresMainQueueSetup
{
  return YES;
}

- (UIView *)view
{
  ARSCNView *arView = [[ARSCNView alloc] initWithFrame:CGRectZero];
  arView.autoenablesDefaultLighting = YES;
  arView.automaticallyUpdatesLighting = YES;

  ARSession *session = [ARSession new];
  ARWorldTrackingConfiguration *config = [ARWorldTrackingConfiguration new];
  if ([ARWorldTrackingConfiguration supportsFrameSemantics:ARFrameSemanticsSceneDepth]) {
    config.frameSemantics = ARFrameSemanticsSceneDepth | ARFrameSemanticsSmoothedSceneDepth;
  }
  config.planeDetection = ARPlaneDetectionHorizontal | ARPlaneDetectionVertical;
  [session runWithConfiguration:config options:ARSessionRunOptionResetTracking | ARSessionRunOptionRemoveExistingAnchors];
  arView.session = session;
  return arView;
}

@end

