import ARKit
import Foundation
import React

@objc(ARPreviewViewManager)
class ARPreviewViewManager: RCTViewManager {
  override static func requiresMainQueueSetup() -> Bool { true }
  override static func moduleName() -> String! { return "ARPreviewView" }

  override func view() -> UIView! {
    let arView = ARSCNView(frame: .zero)
    arView.autoenablesDefaultLighting = true
    arView.automaticallyUpdatesLighting = true

    // Reuse a shared ARSession so capture can access it
    let session = ARSession()
    let configuration = ARWorldTrackingConfiguration()
    if ARWorldTrackingConfiguration.supportsFrameSemantics(.sceneDepth) {
      configuration.frameSemantics = [.sceneDepth, .smoothedSceneDepth]
    }
    configuration.planeDetection = [.horizontal, .vertical]
    session.run(configuration, options: [.resetTracking, .removeExistingAnchors])

    arView.session = session
    ARSessionCoordinator.shared.session = session

    return arVie<PERSON>
  }
}
