import ARKit
import Foundation
import React
import UIKit

@objc(LidarMeasurementModule)
class LidarMeasurementModule: NSObject, RCTBridgeModule {

    static func moduleName() -> String! {
        return "LidarMeasurementModule"
    }

    static func requiresMainQueueSetup() -> Bool {
        return true
    }

    // MARK: - Private Properties
    private var arSession: ARSession?
    private var arView: ARSCNView?
    private var measurementPoints: [simd_float3] = []

    // MARK: - Public Methods

    @objc func isLidarSupported(
        _ resolve: @escaping RCTPromiseResolveBlock,
        rejecter reject: @escaping RCTPromiseRejectBlock
    ) {
        NSLog("🔍 [LidarMeasurement] Checking LiDAR support...")

        DispatchQueue.main.async {
            // Check if device supports LiDAR (scene depth)
            let supportsLidar = ARWorldTrackingConfiguration.supportsFrameSemantics(.sceneDepth)
            NSLog("📱 [LidarMeasurement] LiDAR support: \(supportsLidar ? "YES" : "NO")")
            resolve(supportsLidar)
        }
    }

    @objc func measureDistance(
        _ resolve: @escaping RCTPromiseResolveBlock,
        rejecter reject: @escaping RCTPromiseRejectBlock
    ) {
        NSLog("📏 [LidarMeasurement] Starting distance measurement session...")

        DispatchQueue.main.async {
            self.performMeasurement(resolve: resolve, reject: reject)
        }
    }
    @objc func capturePhotoWithDepth(
        _ resolve: @escaping RCTPromiseResolveBlock,
        rejecter reject: @escaping RCTPromiseRejectBlock
    ) {
        NSLog("📸 [LidarMeasurement] Capturing photo with depth via ARKit...")

        DispatchQueue.main.async {
            guard ARWorldTrackingConfiguration.supportsFrameSemantics(.sceneDepth) else {
                NSLog("❌ [LidarMeasurement] LiDAR not supported on this device")
                reject("LIDAR_NOT_SUPPORTED", "Device does not support LiDAR measurements", nil)
                return
            }

            // Reuse existing preview ARSession if available, otherwise create a short-lived one
            var createdNew = false
            let arSession: ARSession
            if let existing = ARSessionCoordinator.shared.session {
                arSession = existing
            } else {
                arSession = ARSession()
                let configuration = ARWorldTrackingConfiguration()
                configuration.frameSemantics = [.sceneDepth, .smoothedSceneDepth]
                configuration.planeDetection = [.horizontal, .vertical]
                arSession.run(configuration)
                self.arSession = arSession
                createdNew = true
            }

            // Allow a brief warm-up to obtain a valid frame
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.35) {
                self.captureFromCurrentFrame(
                    session: arSession, createdNewSession: createdNew, resolve: resolve,
                    reject: reject)
            }
        }
    }

    private func captureFromCurrentFrame(
        session: ARSession,
        createdNewSession: Bool,
        resolve: @escaping RCTPromiseResolveBlock,
        reject: @escaping RCTPromiseRejectBlock
    ) {
        let startTime = CFAbsoluteTimeGetCurrent()
        guard let frame = session.currentFrame else {
            NSLog("❌ [LidarMeasurement] No current frame for photo capture")
            // Do not tear down shared session here
            reject("NO_FRAME", "No AR frame available for capture", nil)
            return
        }

        // Convert frame.capturedImage (CVPixelBuffer) to JPEG and save to temp file
        let pixelBuffer = frame.capturedImage
        let ciImage = CIImage(cvPixelBuffer: pixelBuffer)
        let context = CIContext(options: nil)

        guard let cgImage = context.createCGImage(ciImage, from: ciImage.extent) else {
            NSLog("❌ [LidarMeasurement] Failed to create CGImage from CIImage")
            // Do not tear down shared session here
            reject("IMAGE_CONVERSION_FAILED", "Could not convert image for saving", nil)
            return
        }

        // Orientation: ARKit's capturedImage is in camera sensor orientation; rotate to portrait
        let uiImage = UIImage(cgImage: cgImage, scale: 1.0, orientation: .right)
        guard let jpegData = uiImage.jpegData(compressionQuality: 0.9) else {
            NSLog("❌ [LidarMeasurement] Failed to create JPEG data")
            // Do not tear down shared session here
            reject("JPEG_FAILED", "Could not create JPEG data", nil)
            return
        }

        let tmpDir = FileManager.default.temporaryDirectory
        let fileName = "koa_capture_\(Int(Date().timeIntervalSince1970 * 1000)).jpg"
        let fileURL = tmpDir.appendingPathComponent(fileName)

        do {
            try jpegData.write(to: fileURL, options: .atomic)
        } catch {
            NSLog("❌ [LidarMeasurement] Failed to write image: \(error.localizedDescription)")
            // Do not tear down shared session here
            reject("FILE_WRITE_FAILED", "Could not save image to temporary file", error)
            return
        }

        // Compute center raycast distance and confidence/quality
        let screenSize = CGSize(width: 390, height: 844)
        let centerPoint = CGPoint(x: screenSize.width * 0.5, y: screenSize.height * 0.5)

        var distances: [String: Float] = [:]
        var validMeasurements = 0
        let pointNames = ["center", "topLeft", "topRight", "bottomLeft", "bottomRight"]
        let points = [
            centerPoint,
            CGPoint(x: screenSize.width * 0.3, y: screenSize.height * 0.3),
            CGPoint(x: screenSize.width * 0.7, y: screenSize.height * 0.3),
            CGPoint(x: screenSize.width * 0.3, y: screenSize.height * 0.7),
            CGPoint(x: screenSize.width * 0.7, y: screenSize.height * 0.7),
        ]

        for (index, point) in points.enumerated() {
            if let distance = self.raycastDistance(from: point, frame: frame) {
                distances[pointNames[index]] = distance
                validMeasurements += 1
            }
        }

        let centerDistance = distances["center"] ?? distances.values.first ?? 0
        let confidence = self.calculateConfidence(distances: Array(distances.values))
        let quality = self.assessQuality(
            confidence: confidence, measurementCount: validMeasurements)
        let processingTime = (CFAbsoluteTimeGetCurrent() - startTime) * 1000

        // Keep session running for preview unless we created a temporary one
        // (handled earlier by createdNewSession flag)
        let result: [String: Any] = [
            "imagePath": "file://\(fileURL.path)",
            "distance": centerDistance,
            "confidence": confidence,
            "quality": quality,
            "measurements": distances,
            "processingTime": processingTime,
        ]

        NSLog(
            "✅ [LidarMeasurement] Photo+depth captured: path=\(fileURL.lastPathComponent), dist=\(String(format: "%.1f", centerDistance))cm"
        )
        resolve(result)
    }

    // MARK: - Private Methods

    private func performMeasurement(
        resolve: @escaping RCTPromiseResolveBlock,
        reject: @escaping RCTPromiseRejectBlock
    ) {
        guard ARWorldTrackingConfiguration.supportsFrameSemantics(.sceneDepth) else {
            NSLog("❌ [LidarMeasurement] LiDAR not supported on this device")
            reject("LIDAR_NOT_SUPPORTED", "Device does not support LiDAR measurements", nil)
            return
        }

        NSLog("🚀 [LidarMeasurement] Creating temporary AR session...")

        // Create temporary AR session for measurement
        let arSession = ARSession()
        let configuration = ARWorldTrackingConfiguration()
        configuration.frameSemantics = [.sceneDepth, .smoothedSceneDepth]
        configuration.planeDetection = [.horizontal, .vertical]

        // Start session
        arSession.run(configuration)
        self.arSession = arSession

        // Wait a brief moment for session to initialize, then take measurement
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.takeMeasurement(session: arSession, resolve: resolve, reject: reject)
        }
    }

    private func takeMeasurement(
        session: ARSession,
        resolve: @escaping RCTPromiseResolveBlock,
        reject: @escaping RCTPromiseRejectBlock
    ) {
        let startTime = CFAbsoluteTimeGetCurrent()
        NSLog("📐 [LidarMeasurement] Taking raycast measurements...")

        guard let frame = session.currentFrame else {
            NSLog("❌ [LidarMeasurement] No current frame available")
            reject("NO_FRAME", "No AR frame available for measurement", nil)
            return
        }

        // Convert normalized points to screen coordinates
        let screenSize = CGSize(width: 390, height: 844)  // iPhone screen size approximation
        let measurementPoints = [
            CGPoint(x: screenSize.width * 0.5, y: screenSize.height * 0.5),  // Center
            CGPoint(x: screenSize.width * 0.3, y: screenSize.height * 0.3),  // Top-left
            CGPoint(x: screenSize.width * 0.7, y: screenSize.height * 0.3),  // Top-right
            CGPoint(x: screenSize.width * 0.3, y: screenSize.height * 0.7),  // Bottom-left
            CGPoint(x: screenSize.width * 0.7, y: screenSize.height * 0.7),  // Bottom-right
        ]

        var distances: [String: Float] = [:]
        var validMeasurements = 0

        // Take measurements at multiple points
        let pointNames = ["center", "topLeft", "topRight", "bottomLeft", "bottomRight"]

        for (index, point) in measurementPoints.enumerated() {
            if let distance = self.raycastDistance(from: point, frame: frame) {
                distances[pointNames[index]] = distance
                validMeasurements += 1
                NSLog(
                    "📍 [LidarMeasurement] \(pointNames[index]): \(String(format: "%.2f", distance))cm"
                )
            }
        }

        guard validMeasurements > 0 else {
            NSLog("❌ [LidarMeasurement] No valid measurements obtained")
            reject("NO_MEASUREMENTS", "Could not obtain any distance measurements", nil)
            return
        }

        // Calculate center distance (primary measurement)
        let centerDistance = distances["center"] ?? distances.values.first ?? 0

        // Calculate confidence based on measurement consistency
        let confidence = self.calculateConfidence(distances: Array(distances.values))
        let quality = self.assessQuality(
            confidence: confidence, measurementCount: validMeasurements)

        let processingTime = (CFAbsoluteTimeGetCurrent() - startTime) * 1000

        let result: [String: Any] = [
            "distance": centerDistance,
            "confidence": confidence,
            "quality": quality,
            "measurements": distances,
            "processingTime": processingTime,
        ]

        NSLog(
            "✅ [LidarMeasurement] Measurement complete: \(String(format: "%.1f", centerDistance))cm, confidence: \(String(format: "%.2f", confidence)), quality: \(quality)"
        )

        resolve(result)
    }

    private func raycastDistance(from point: CGPoint, frame: ARFrame) -> Float? {
        // Create raycast query from screen point
        let raycastQuery = frame.session.raycastQuery(
            from: point,
            allowing: .estimatedPlane,
            alignment: .any
        )

        guard let query = raycastQuery else {
            return nil
        }

        // Perform raycast
        let raycastResults = frame.session.raycast(query)

        guard let firstResult = raycastResults.first else {
            return nil
        }

        // Calculate distance from camera to hit point
        let cameraPosition = simd_float3(
            frame.camera.transform.columns.3.x,
            frame.camera.transform.columns.3.y,
            frame.camera.transform.columns.3.z
        )
        let hitPosition = simd_float3(
            firstResult.worldTransform.columns.3.x,
            firstResult.worldTransform.columns.3.y,
            firstResult.worldTransform.columns.3.z
        )
        let distance = simd_distance(cameraPosition, hitPosition)

        // Convert to centimeters and ensure reasonable range
        let distanceCM = distance * 100
        return (distanceCM > 5 && distanceCM < 500) ? distanceCM : nil
    }

    private func calculateConfidence(distances: [Float]) -> Float {
        guard distances.count > 1 else { return 0.5 }

        let mean = distances.reduce(0, +) / Float(distances.count)
        let variance = distances.map { pow($0 - mean, 2) }.reduce(0, +) / Float(distances.count)
        let standardDeviation = sqrt(variance)

        // Lower standard deviation = higher confidence
        let normalizedStdDev = min(standardDeviation / mean, 1.0)
        return max(0.1, 1.0 - normalizedStdDev)
    }

    private func assessQuality(confidence: Float, measurementCount: Int) -> String {
        if confidence > 0.8 && measurementCount >= 4 {
            return "excellent"
        } else if confidence > 0.6 && measurementCount >= 3 {
            return "good"
        } else if confidence > 0.4 && measurementCount >= 2 {
            return "fair"
        } else {
            return "poor"
        }
    }

    private func cleanupSession() {
        NSLog("🧹 [LidarMeasurement] Cleaning up AR session...")
        DispatchQueue.main.async {
            self.arSession?.pause()
            self.arSession = nil
            self.arView = nil
        }
    }
}
