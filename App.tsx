import 'react-native-url-polyfill/auto';
import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  SafeAreaView,
  StyleSheet,
  StatusBar,
  Alert,
  View,
  TouchableOpacity,
  ScrollView,
  Linking,
  AppState,
} from 'react-native';
import { CameraScreen } from './src/components/CameraScreen';
import { FoodResultsScreen } from './src/components/FoodResultsScreen';
import { HistoryScreen } from './src/components/HistoryScreen';
import {
  BrutalistText,
  BrutalistTabBar,
  TabKey,
  AuthNavigator,
  LoadingScreen,
  SetNewPasswordScreen,
  SwipeableItem,
  SwipeableItemRef,
  DeleteConfirmationModal,
  MealEditModal,
  AppHeader,
  ProfileModal,
  EmailVerificationSuccessModal,
  EmailVerificationErrorModal,
} from './src/components';
import { BrutalistTheme } from './src/theme/colors';
import { LoggedFood, MealSession } from './src/types/food';
import { RecognitionResult, aiService } from './src/services/aiService';
import { OPENAI_API_KEY } from '@env';
import { AuthProvider, useAuth } from './src/contexts/AuthContext';
import { foodLogService } from './src/services/foodLogService';
import { authService } from './src/services/authService';
import { supabase } from './src/services/supabase';

const AppContent: React.FC = () => {
  const {
    user,
    profile,
    loading,
    isPasswordRecovery,
    completePasswordRecovery,
    setPasswordRecoveryMode,
    setRecoveryTokens,
  } = useAuth();
  const [activeTab, setActiveTab] = useState<TabKey>('today');
  const [recognitionResult, setRecognitionResult] =
    useState<RecognitionResult | null>(null);
  const [mealSessions, setMealSessions] = useState<MealSession[]>([]);
  const [showFoodResults, setShowFoodResults] = useState(false);
  const [expandedSessions, setExpandedSessions] = useState<Set<string>>(
    new Set(),
  );
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    visible: boolean;
    type: 'session' | 'food';
    sessionId?: string;
    foodId?: string;
    title: string;
    message: string;
  } | null>(null);
  const [editFood, setEditFood] = useState<{
    visible: boolean;
    food: LoggedFood | null;
    session: MealSession | null;
  }>({ visible: false, food: null, session: null });
  const [openSwipeItemId, setOpenSwipeItemId] = useState<string | null>(null);
  const swipeableRefs = useRef<Map<string, SwipeableItemRef>>(new Map());
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [emailVerificationSuccess, setEmailVerificationSuccess] = useState<{
    visible: boolean;
    userEmail?: string;
  }>({ visible: false });
  const [emailVerificationError, setEmailVerificationError] = useState<{
    visible: boolean;
    errorCode?: string;
    errorMessage?: string;
  }>({ visible: false });

  // Initialize AI service API key from environment variables
  React.useEffect(() => {
    if (OPENAI_API_KEY) {
      aiService.setApiKey(OPENAI_API_KEY);
      console.log('OpenAI API key configured');
    } else {
      console.warn('OpenAI API key not found. Please check your .env file.');
    }
  }, []);

  // Load all foods from Supabase when user is available
  React.useEffect(() => {
    if (user) {
      loadAllFoods();
    }
  }, [user, loadAllFoods]);

  // Handle deep links for email verification
  useEffect(() => {
    const parseSupabaseUrl = (url: string) => {
      // Supabase uses # fragments but we need ? for URL params
      if (url.includes('#')) {
        return url.replace('#', '?');
      }
      return url;
    };

    const handleDeepLink = async (url: string) => {
      console.log('Deep link received:', url);

      if (url && (url.includes('://auth') || url.includes('koa://auth'))) {
        console.log('Auth deep link detected');

        // Parse Supabase URL format (replace # with ?)
        const parsedUrl = parseSupabaseUrl(url);
        const urlObj = new URL(parsedUrl);
        const params = urlObj.searchParams;

        console.log('Parsed URL params:', Object.fromEntries(params.entries()));

        // Check for error parameters first
        const error = params.get('error');
        const error_code = params.get('error_code');
        const error_description = params.get('error_description');

        if (error || error_code) {
          console.log('Auth error detected:', {
            error,
            error_code,
            error_description,
          });

          // Show error modal for both OAuth and email verification errors
          setTimeout(() => {
            setEmailVerificationError({
              visible: true,
              errorCode: error_code || error,
              errorMessage: error_description
                ? decodeURIComponent(error_description.replace(/\+/g, ' '))
                : undefined,
            });
          }, 500);
          return;
        }

        // If no errors, check for success tokens
        const access_token = params.get('access_token');
        const refresh_token = params.get('refresh_token');
        const type = params.get('type'); // Check if this is a password recovery
        const provider = params.get('provider'); // Check if this is OAuth

        if (access_token && refresh_token) {
          console.log(
            'Setting session with tokens, type:',
            type,
            'provider:',
            provider,
          );

          // Check if this is a password recovery link
          if (type === 'recovery') {
            console.log(
              'Password recovery link detected - storing tokens and entering recovery mode',
            );
            try {
              // Store recovery tokens for later use
              setRecoveryTokens({ access_token, refresh_token });
              // Set recovery mode to show password reset screen
              setPasswordRecoveryMode(true);
              console.log('Recovery tokens stored and recovery mode enabled');
            } catch (err) {
              console.error('Recovery setup failed:', err);
            }
            return;
          }

          // Handle OAuth or email verification
          try {
            const { data, error: sessionError } =
              await supabase.auth.setSession({
                access_token,
                refresh_token,
              });

            if (!sessionError && data.session?.user) {
              console.log(
                'Authentication successful!',
                provider ? `via ${provider}` : 'via email verification',
              );

              // Create profile if it doesn't exist (for both OAuth and email verification)
              try {
                const { data: existingProfile } = await authService.getProfile(
                  data.session.user.id,
                );
                if (!existingProfile) {
                  const displayName =
                    data.session.user.user_metadata?.full_name ||
                    data.session.user.user_metadata?.name ||
                    data.session.user.email?.split('@')[0] ||
                    'User';

                  await authService.createProfile(data.session.user.id, {
                    full_name: displayName,
                  });
                }
              } catch (profileErr) {
                console.warn('Profile creation/check failed:', profileErr);
              }

              // Show success message
              setTimeout(() => {
                setEmailVerificationSuccess({
                  visible: true,
                  userEmail: data.session.user?.email,
                });
              }, 500);
            } else {
              console.error('Session setup failed:', sessionError);
            }
          } catch (err) {
            console.error('Session setup failed:', err);
          }
        }
      }
    };

    // Set up listeners
    Linking.getInitialURL().then(url => {
      if (url) handleDeepLink(url);
    });

    const subscription = Linking.addEventListener('url', ({ url }) => {
      handleDeepLink(url);
    });

    return () => subscription?.remove();
  }, []);

  // Handle app state changes to refresh data when app resumes
  useEffect(() => {
    const handleAppStateChange = async (nextAppState: string) => {
      if (nextAppState === 'active' && user) {
        console.log('App resumed - cleaning up sync queue and refreshing data');
        try {
          // Note: Sync cleanup now handled by AuthContext for better coordination

          // Refresh data when app comes to foreground
          await loadAllFoods();
        } catch (error) {
          console.error('Error refreshing data on app resume:', error);
        }
      }
    };

    const appStateSubscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => appStateSubscription?.remove();
  }, [user, loadAllFoods]);

  const loadAllFoods = useCallback(async () => {
    if (!user) return;

    // Load ALL meal sessions (grouped by meal_session_id) for history screen
    const { data: sessions, error } = await foodLogService.getMealSessions(
      user.id,
      null,
    );
    if (error) {
      console.error('Failed to load meal sessions:', error);
      return;
    }

    if (sessions) {
      setMealSessions(sessions);
    }
  }, [user]);

  // Deletion handlers for Today screen
  const handleDeleteFood = (
    foodId: string,
    sessionId: string,
    foodName: string,
  ) => {
    setDeleteConfirmation({
      visible: true,
      type: 'food',
      sessionId,
      foodId,
      title: 'DELETE FOOD ITEM',
      message: `Remove "${foodName}" from today's ${
        sessionId ? 'meal' : 'log'
      }? This will update your daily totals.`,
    });
  };

  const handleDeleteSession = (
    sessionId: string,
    mealType: string,
    itemCount: number,
  ) => {
    setDeleteConfirmation({
      visible: true,
      type: 'session',
      sessionId,
      title: 'DELETE MEAL SESSION',
      message: `Remove this entire ${mealType} session (${itemCount} items) from today? This will update your daily totals.`,
    });
  };

  const confirmDeletion = async () => {
    if (!deleteConfirmation || !user) return;

    try {
      if (deleteConfirmation.type === 'food' && deleteConfirmation.foodId) {
        const { error } = await foodLogService.deleteFoodItem(
          user.id,
          deleteConfirmation.foodId,
        );
        if (error) throw error;
      } else if (
        deleteConfirmation.type === 'session' &&
        deleteConfirmation.sessionId
      ) {
        const { error } = await foodLogService.deleteMealSession(
          user.id,
          deleteConfirmation.sessionId,
        );
        if (error) throw error;
      }

      // Refresh data
      await loadAllFoods();
    } catch (error) {
      Alert.alert('Error', 'Failed to delete item. Please try again.');
      console.error('Delete error:', error);
    }

    setDeleteConfirmation(null);
  };

  const cancelDeletion = () => {
    setDeleteConfirmation(null);
  };

  // Edit handlers for Today screen
  const handleEditMealSession = (session: MealSession) => {
    setEditFood({ visible: true, food: null, session });
  };

  const handleSaveEditedFood = async (editedFood: LoggedFood) => {
    if (!user) return;

    console.log(
      'handleSaveEditedFood called with:',
      editedFood.name,
      'quantity:',
      editedFood.quantity,
    );

    try {
      const { error } = await foodLogService.updateFoodItem(
        user.id,
        editedFood.id,
        editedFood,
      );

      if (error) {
        console.error('Database update error:', error);
        throw error;
      }

      console.log('Successfully updated food in database');

      // Refresh data
      await loadAllFoods();

      // Update daily summary
      const today = new Date().toISOString().split('T')[0];
      await foodLogService.updateDailySummary(user.id, today);
    } catch (error) {
      Alert.alert('Error', 'Failed to update food item. Please try again.');
      console.error('Update error:', error);
    }
  };

  const handleCancelEdit = () => {
    setEditFood({ visible: false, food: null, session: null });
  };

  const handleAddFoodToSession = async (
    sessionId: string,
    food: Omit<LoggedFood, 'id' | 'meal_session_id'>,
  ) => {
    if (!user) return;
    try {
      // We only need to pass the raw food data, the service handles the rest
      const { id, meal_session_id, ...newFoodData } = food;
      await foodLogService.addFoodToSession(user.id, sessionId, newFoodData);
      await loadAllFoods(); // Refresh data
    } catch (error) {
      Alert.alert(
        'Error',
        'Failed to add new food to the meal. Please try again.',
      );
      console.error('Add food to session error:', error);
    }
  };

  // Swipe management handlers for Today screen
  const handleSwipeStart = (itemId: string) => {
    // Close any currently open item
    if (openSwipeItemId && openSwipeItemId !== itemId) {
      const currentOpenRef = swipeableRefs.current.get(openSwipeItemId);
      if (currentOpenRef) {
        currentOpenRef.reset();
      }
    }
    setOpenSwipeItemId(itemId);
  };

  const handleSwipeReset = () => {
    setOpenSwipeItemId(null);
  };

  const setSwipeableRef = (itemId: string, ref: SwipeableItemRef | null) => {
    if (ref) {
      swipeableRefs.current.set(itemId, ref);
    } else {
      swipeableRefs.current.delete(itemId);
    }
  };

  const handleFoodRecognized = (result: RecognitionResult) => {
    setRecognitionResult(result);
    setShowFoodResults(true);
  };

  const handleLogFood = async (food: LoggedFood) => {
    if (!user) {
      Alert.alert('Error', 'You must be logged in to save food logs');
      return;
    }

    try {
      // Save to Supabase as a single-item meal session for consistency
      const { data: savedFoods, error } = await foodLogService.logMealSession(
        user.id,
        [food],
      );

      if (error) {
        console.error('Failed to save food:', error);
        Alert.alert('Error', 'Failed to save food log. Please try again.');
        return;
      }

      if (savedFoods) {
        // Reload all foods to ensure consistency
        await loadAllFoods();

        // Update daily summary
        const today = new Date().toISOString().split('T')[0];
        await foodLogService.updateDailySummary(user.id, today);
      }

      setShowFoodResults(false);
      setRecognitionResult(null);

      // Navigate directly to today view
      setActiveTab('today');
    } catch (err) {
      console.error('Error saving food:', err);
      Alert.alert('Error', 'Failed to save food log. Please try again.');
    }
  };

  const handleLogMealSession = async (foods: LoggedFood[]) => {
    if (!user) {
      Alert.alert('Error', 'You must be logged in to save food logs');
      return;
    }

    try {
      // Save all foods as a meal session to Supabase
      const { data: savedFoods, error } = await foodLogService.logMealSession(
        user.id,
        foods,
      );

      if (error) {
        console.error('Failed to save meal session:', error);
        Alert.alert('Error', 'Failed to save meal. Please try again.');
        return;
      }

      if (savedFoods) {
        // Reload all foods to ensure consistency
        await loadAllFoods();

        // Update daily summary
        const today = new Date().toISOString().split('T')[0];
        await foodLogService.updateDailySummary(user.id, today);
      }

      setShowFoodResults(false);
      setRecognitionResult(null);

      // Navigate directly to today view
      setActiveTab('today');
    } catch (err) {
      console.error('Error saving meal session:', err);
      Alert.alert('Error', 'Failed to save meal. Please try again.');
    }
  };

  const handleRetakePicture = () => {
    setRecognitionResult(null);
    setShowFoodResults(false);
  };

  const handleManualAdd = () => {
    // Create empty recognition result to show the food results screen
    setRecognitionResult({
      foods: [],
      overall_confidence: 100
    });
    setShowFoodResults(true);
  };

  const handleTabPress = (tab: TabKey) => {
    setActiveTab(tab);
    // Clear food results when switching tabs
    if (tab !== 'scan' && showFoodResults) {
      setShowFoodResults(false);
      setRecognitionResult(null);
    }
  };

  const handleProfilePress = () => {
    setShowProfileModal(true);
  };

  const handleCloseProfile = () => {
    setShowProfileModal(false);
  };

  const handleCloseEmailVerification = () => {
    setEmailVerificationSuccess({ visible: false });
  };

  const handleCloseEmailVerificationError = () => {
    setEmailVerificationError({ visible: false });
  };

  // Dynamic StatusBar style based on active tab
  const getStatusBarStyle = () => {
    return activeTab === 'scan' ? 'light-content' : 'dark-content';
  };

  const getStatusBarBackground = () => {
    return activeTab === 'scan'
      ? BrutalistTheme.colors.black
      : BrutalistTheme.colors.white;
  };

  const toggleSessionExpanded = (sessionId: string) => {
    setExpandedSessions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sessionId)) {
        newSet.delete(sessionId);
      } else {
        newSet.add(sessionId);
      }
      return newSet;
    });
  };

  const getTodaysNutrition = () => {
    const today = new Date().toDateString();
    const todaysSessions = mealSessions.filter(
      session => new Date(session.logged_at).toDateString() === today,
    );

    // Flatten all foods from today's sessions
    const todaysFoods = todaysSessions.flatMap(session => session.foods);

    return {
      calories: todaysFoods.reduce(
        (sum, food) => sum + food.calories * food.quantity,
        0,
      ),
      protein: todaysFoods.reduce(
        (sum, food) => sum + food.protein * food.quantity,
        0,
      ),
      carbs: todaysFoods.reduce(
        (sum, food) => sum + food.carbs * food.quantity,
        0,
      ),
      fat: todaysFoods.reduce((sum, food) => sum + food.fat * food.quantity, 0),
      sessions: todaysSessions,
      sessionCount: todaysSessions.length,
    };
  };

  const renderTodayScreen = () => {
    const nutrition = getTodaysNutrition();

    return (
      <View style={styles.screenContainer}>
        <AppHeader
          title="KOA"
          variant="minimal"
          onProfilePress={handleProfilePress}
        />

        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.content}>
            {/* Simple Today's Summary - matches FoodResults style */}
            <View style={styles.summarySection}>
              <BrutalistText
                variant="caption1"
                weight="medium"
                color="grey700"
                fontFamily="mono"
                style={styles.summaryHeader}
              >
                TODAY'S NUTRITION
              </BrutalistText>

              {/* Clean metrics row */}
              <View style={styles.todayMetrics}>
                <View style={styles.metricItem}>
                  <BrutalistText
                    variant="title3"
                    weight="bold"
                    fontFamily="mono"
                    color="grey800"
                  >
                    {nutrition.sessionCount}
                  </BrutalistText>
                  <BrutalistText
                    variant="caption2"
                    weight="medium"
                    color="grey600"
                  >
                    MEALS
                  </BrutalistText>
                </View>

                <View style={styles.metricItem}>
                  <BrutalistText
                    variant="title3"
                    weight="bold"
                    fontFamily="mono"
                    color="grey900"
                  >
                    {Math.round((nutrition.calories / (profile?.daily_calorie_goal || 2000)) * 100)}%
                  </BrutalistText>
                  <BrutalistText
                    variant="caption2"
                    weight="medium"
                    color="grey600"
                  >
                    GOAL
                  </BrutalistText>
                </View>

                <View style={styles.metricItem}>
                  <BrutalistText
                    variant="title3"
                    weight="bold"
                    fontFamily="mono"
                    color="accent1"
                  >
                    {Math.round(nutrition.calories)}
                  </BrutalistText>
                  <BrutalistText
                    variant="caption2"
                    weight="medium"
                    color="grey600"
                  >
                    CALORIES
                  </BrutalistText>
                </View>
              </View>
            </View>

            {/* Recent meals - clean list showing meal sessions */}
            {nutrition.sessions.length > 0 && (
              <View style={styles.recentMealsSection}>
                <BrutalistText
                  variant="caption1"
                  weight="medium"
                  color="grey700"
                  fontFamily="mono"
                  style={styles.sectionHeader}
                >
                  RECENT MEALS
                </BrutalistText>

                <View style={styles.mealsContainer}>
                  {nutrition.sessions.slice(0, 4).map((session, _index) => {
                    const isExpanded = expandedSessions.has(session.id);
                    return (
                      <SwipeableItem
                        key={session.id}
                        ref={ref =>
                          setSwipeableRef(`today-session-${session.id}`, ref)
                        }
                        itemId={`today-session-${session.id}`}
                        onDelete={() =>
                          handleDeleteSession(
                            session.id,
                            session.meal_type,
                            session.total_items,
                          )
                        }
                        onEdit={() => handleEditMealSession(session)}
                        onSwipeStart={handleSwipeStart}
                        onSwipeReset={handleSwipeReset}
                        deleteText="DELETE"
                        editText="EDIT"
                        deleteThreshold={40}
                      >
                        <TouchableOpacity
                          style={styles.mealSessionContainer}
                          onPress={() => toggleSessionExpanded(session.id)}
                          activeOpacity={0.7}
                        >
                          <View style={styles.mealRow}>
                            <View style={styles.mealInfo}>
                              <View style={styles.timeAndMealGroup}>
                                <BrutalistText
                                  variant="caption1"
                                  weight="medium"
                                  color="grey600"
                                  fontFamily="mono"
                                  style={styles.timeLabel}
                                >
                                  {new Date(
                                    session.logged_at,
                                  ).toLocaleTimeString([], {
                                    hour: '2-digit',
                                    minute: '2-digit',
                                  })}
                                </BrutalistText>
                                <BrutalistText
                                  variant="callout"
                                  color="grey700"
                                  weight="bold"
                                  fontFamily="mono"
                                >
                                  {session.meal_type.charAt(0).toUpperCase() +
                                    session.meal_type.slice(1)}
                                </BrutalistText>
                              </View>
                              <View>
                                <BrutalistText
                                  variant="caption2"
                                  weight="medium"
                                  color={isExpanded ? 'accent2' : 'grey600'}
                                  fontFamily="mono"
                                >
                                  {session.total_items} ITEMS{' '}
                                  {isExpanded ? '−' : '+'}
                                </BrutalistText>
                              </View>
                            </View>

                            <BrutalistText
                              variant="callout"
                              weight="semibold"
                              color="accent1"
                              fontFamily="mono"
                            >
                              {Math.round(session.total_calories)}
                            </BrutalistText>
                          </View>

                          {/* Expanded Individual Foods */}
                          {isExpanded && (
                            <View style={styles.expandedFoods}>
                              {session.foods.map((food, foodIndex) => (
                                <SwipeableItem
                                  key={`${food.id}-${foodIndex}`}
                                  ref={ref =>
                                    setSwipeableRef(
                                      `today-food-${food.id}-${foodIndex}`,
                                      ref,
                                    )
                                  }
                                  itemId={`today-food-${food.id}-${foodIndex}`}
                                  onDelete={() =>
                                    handleDeleteFood(
                                      food.id,
                                      session.id,
                                      food.name,
                                    )
                                  }
                                  onSwipeStart={handleSwipeStart}
                                  onSwipeReset={handleSwipeReset}
                                  deleteText="DEL"
                                  editText="EDIT"
                                  deleteThreshold={40}
                                >
                                  <View style={styles.foodItemRow}>
                                    <BrutalistText
                                      variant="caption2"
                                      weight="medium"
                                      color="grey600"
                                      fontFamily="mono"
                                    >
                                      {food.name} • {food.quantity}×
                                    </BrutalistText>
                                  </View>
                                </SwipeableItem>
                              ))}
                            </View>
                          )}
                        </TouchableOpacity>
                      </SwipeableItem>
                    );
                  })}

                  {nutrition.sessions.length > 4 && (
                    <BrutalistText
                      variant="caption1"
                      color="grey600"
                      fontFamily="serif"
                      style={styles.moreItemsText}
                    >
                      +{nutrition.sessions.length - 4} more in history
                    </BrutalistText>
                  )}
                </View>
              </View>
            )}

            {/* Empty state */}
            {nutrition.sessions.length === 0 && (
              <View style={styles.emptyState}>
                <BrutalistText
                  variant="title3"
                  weight="medium"
                  color="grey600"
                  style={styles.emptyMessage}
                >
                  No meals logged today
                </BrutalistText>
                <BrutalistText
                  variant="body"
                  color="grey600"
                  fontFamily="serif"
                >
                  Start by scanning your first meal
                </BrutalistText>
              </View>
            )}
          </View>
        </ScrollView>

        {/* Delete Confirmation Modal */}
        {deleteConfirmation && (
          <DeleteConfirmationModal
            visible={deleteConfirmation.visible}
            title={deleteConfirmation.title}
            message={deleteConfirmation.message}
            onConfirm={confirmDeletion}
            onCancel={cancelDeletion}
            confirmText="DELETE"
            cancelText="CANCEL"
          />
        )}
      </View>
    );
  };

  // Show FoodResults screen as a modal overlay when food is recognized
  if (showFoodResults && recognitionResult) {
    return (
      <FoodResultsScreen
        foods={recognitionResult.foods}
        confidence={recognitionResult.overall_confidence}
        onLogFood={handleLogFood}
        onLogMealSession={handleLogMealSession}
        onRetakePicture={handleRetakePicture}
      />
    );
  }

  // Main tab-based navigation
  const renderCurrentTab = () => {
    switch (activeTab) {
      case 'scan':
        return (
          <CameraScreen 
            onFoodRecognized={handleFoodRecognized} 
            onManualAdd={handleManualAdd}
          />
        );

      case 'today':
        return renderTodayScreen();

      case 'history':
        return (
          <HistoryScreen
            mealSessions={mealSessions}
            userId={user?.id || ''}
            onDataChanged={loadAllFoods}
            onEditMealSession={handleEditMealSession}
            onProfilePress={handleProfilePress}
          />
        );

      default:
        return renderTodayScreen();
    }
  };

  // Show loading screen while checking auth state
  if (loading) {
    return (
      <>
        <LoadingScreen />
        {/* Email Verification Modals - Available during loading */}
        <EmailVerificationSuccessModal
          visible={emailVerificationSuccess.visible}
          onClose={handleCloseEmailVerification}
          userEmail={emailVerificationSuccess.userEmail}
        />
        <EmailVerificationErrorModal
          visible={emailVerificationError.visible}
          onClose={handleCloseEmailVerificationError}
          errorCode={emailVerificationError.errorCode}
          errorMessage={emailVerificationError.errorMessage}
        />
      </>
    );
  }

  // Show password recovery screen if in password recovery mode
  if (isPasswordRecovery) {
    return <SetNewPasswordScreen onComplete={completePasswordRecovery} />;
  }

  // Show auth screens if not logged in
  if (!user) {
    return (
      <>
        <AuthNavigator />
        {/* Email Verification Modals - Available during auth flow */}
        <EmailVerificationSuccessModal
          visible={emailVerificationSuccess.visible}
          onClose={handleCloseEmailVerification}
          userEmail={emailVerificationSuccess.userEmail}
        />
        <EmailVerificationErrorModal
          visible={emailVerificationError.visible}
          onClose={handleCloseEmailVerificationError}
          errorCode={emailVerificationError.errorCode}
          errorMessage={emailVerificationError.errorMessage}
        />
      </>
    );
  }

  // Show main app if logged in
  return (
    <SafeAreaView style={styles.appContainer}>
      <StatusBar
        barStyle={getStatusBarStyle()}
        backgroundColor={getStatusBarBackground()}
      />
      <View style={styles.mainContent}>{renderCurrentTab()}</View>
      <BrutalistTabBar activeTab={activeTab} onTabPress={handleTabPress} />

      {/* Profile Modal - Available on all screens */}
      <ProfileModal visible={showProfileModal} onClose={handleCloseProfile} />

      {/* Edit Meal Modal - Available on all screens */}
      <MealEditModal
        visible={editFood.visible}
        session={editFood.session}
        onSave={handleSaveEditedFood}
        onAddFoodToSession={handleAddFoodToSession}
        onCancel={handleCancelEdit}
      />
    </SafeAreaView>
  );
};

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

const styles = StyleSheet.create({
  appContainer: {
    flex: 1,
    backgroundColor: BrutalistTheme.colors.white,
  },
  mainContent: {
    flex: 1,
  },
  screenContainer: {
    flex: 1,
    backgroundColor: BrutalistTheme.colors.white,
  },
  container: {
    flex: 1,
    backgroundColor: BrutalistTheme.colors.white,
  },
  scrollView: {
    flex: 1,
  },

  // Content Layout
  content: {
    flex: 1,
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingTop: BrutalistTheme.spacing.sm,
    paddingBottom: BrutalistTheme.spacing.lg,
    gap: BrutalistTheme.spacing.sm,
  },

  // Summary Section - clean like FoodResults
  summarySection: {
    backgroundColor: BrutalistTheme.colors.white,
    paddingTop: 0,
    paddingBottom: BrutalistTheme.spacing.sm,
    paddingHorizontal: BrutalistTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey200,
    alignItems: 'center',
    gap: BrutalistTheme.spacing.sm,
  },
  summaryHeader: {
    letterSpacing: 1,
  },

  // Today's Metrics - clean grid
  todayMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  metricItem: {
    alignItems: 'center',
    flex: 1,
    gap: 1,
  },

  // Recent Meals Section
  recentMealsSection: {
    gap: 0,
  },
  sectionHeader: {
    letterSpacing: 1,
    marginBottom: 0,
    paddingHorizontal: BrutalistTheme.spacing.md,
    opacity: 0.9,
  },
  mealsContainer: {
    gap: 0,
  },
  mealSessionContainer: {
    backgroundColor: BrutalistTheme.colors.white,
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey200,
    paddingBottom: 0,
    marginBottom: 0,
  },
  mealRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: BrutalistTheme.spacing.xs,
    paddingHorizontal: BrutalistTheme.spacing.md,
    backgroundColor: BrutalistTheme.colors.white,
  },
  expandedFoods: {
    paddingBottom: 1,
    paddingLeft: BrutalistTheme.spacing.md,
    gap: 1,
  },
  foodItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: BrutalistTheme.spacing.xs,
  },
  foodItemContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: BrutalistTheme.spacing.sm,
  },
  mealInfo: {
    flex: 1,
    gap: 0,
  },
  timeAndMealGroup: {
    gap: 0,
  },
  timeLabel: {
    letterSpacing: 0.5,
    marginBottom: -4,
  },
  moreItemsText: {
    textAlign: 'center',
    marginTop: BrutalistTheme.spacing.sm,
    fontStyle: 'italic',
  },

  // Empty State
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: BrutalistTheme.spacing.xxxl,
    gap: BrutalistTheme.spacing.md,
  },
  emptyMessage: {
    textAlign: 'center',
  },
  foodItemRow: {
    paddingVertical: 0,
    backgroundColor: BrutalistTheme.colors.white,
  },
});

export default App;
